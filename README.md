# COSMETRICS AI README

[![Deploy to AWS EKS](https://github.com/Cosmetrics-Ai/Cosm2-Software-Development/actions/workflows/deploy-to-eks.yml/badge.svg)](https://github.com/Cosmetrics-Ai/Cosm2-Software-Development/actions/workflows/deploy-to-eks.yml)

## BE CAREFUL THIS DOC NEEDS UPDATING

## Plain Run and Deployment and

Using Pipenv to manage dependencies and the project, once the project is cloned, run the commands below to start the backend:

```bash
# at the root of the project dqcce2l92
python -V # check what version of Python being used in the terminal
pip install pipenv

pipenv shell
# Check if any module is installed 
pipenv run pip freeze | wc -l

# Install Dependencies 
pipenv install # installs just the required dependencies
pipenv install --dev # install all dependencies including the dev (development) dependencies

# Get help for pipenv

pipenv -h

# Load Fixture data
python manage.py loaddata <app_name> e.g. products

#Setup and  run Backend/API Services

make help

  @echo "Available commands:"
 @echo "  make print_settings"
 @echo "  make makemigrations  - Apply Django makemigrations"
 @echo "  make migrate     - Apply Django migrations"
 @echo "  make products   - Seed Products table"
 @echo "  make questions   - Seed Questionnaire table"
 @echo "  make seed_users   - Seed Users table"
 @echo "  make run - Start the backend Django server"
 @echo "  make collectstatic   - Collect media assets into media dir"
 @echo "  make run_tests   - Run Django configured tests"


#The Backend Services and API should be running.
#Check the API: http://127.0.0.1:8000/api/docs#"

```

## Requirements

Docker

1. Install Docker desktop and open it to start docker daemon

- <https://docs.docker.com/get-docker/>

2. Install Docker without GUI

- <https://docs.docker.com/get-docker/> (find download instruction a bit further down after selecting OS)

## Setup

1. Pick a location for git repository
2. Download this git repo

- https : git clone <https://github.com/Cosm1/Software-Development-.git>
- ssh : git clone <**************>:Cosm1/Software-Development-.git

3. Change into git repo directory

- cd Software-Development-

4. Build image
   - docker compose build
5. Run container(make sure you are in repo volume command is relative)
   - docker compose up
6. Check if there are no issues by going to http:localhost:8000
7. Activate pip environment by typing "python3 -m pipenv shell" from base folder with Pipfile and manage.py
8. run 'pip install -r requirements.txt' to install project dependencies
9. While in pip environment. You can run project commands as normal with python i.e

- python manage.py startapp [name of app]
- run: [WINDOWS] python manage.py runserver [to start the development server]
- run: [MAC] python3 manage.py runserver [to start the development server]

## If Docker fails

- Use a virtual environment with requirements file. i.e pipenv, virtualenv(venv) etc.
- You can install the library through pip
  - pipenv
  - python3 -m pip install pipenv
- Create and enter environment
  - python3 -m pipenv shell
- Create a postgres database
- Create a .env file and copy the environment variables in env.sample to your .env file
- Install requirements
  - pip install -r requirements.txt
- Make sure to enter environment everytime, some editors will allow you to set them i.e vscode

## Extra

Run these commands while in pip environment
To make migrations after 'saving' changes

- python manage.py makemigrations

Updating requirements.txt

- pip freeze > requirements.txt

To apply migration to database

- python manage.py migrate

To create admin user

- python manage.py createsuperuser

To access the shell on container while docker container is running

- docker compose exec django_server bash

To get logs

- docker logs cosmetrics_server

To check info on container

- docker inspec cosmetrics_server

To stop container

- docker stop cosmetrics_server

To remove container

- docker rm cosmetrics_server

## Frontend Tooling

cd into the src folder where we have all the frontend tooling including a package.json file

- npm install [to install dependencies]
- npm run compile:css:watch [to start watching for file changes in scss]

## Operational Commands

```bash

djoser  - logout
#TODO: Retake questionnaire: uri: /retake
#

docker compose exec -it web python manage.py makemigrations
docker compose exec -it web python manage.py migrate

docker compose exec -it web python manage.py createsuperuser

docker compose exec -it web python manage.py runserver
docker compose exec -it web python manage.py populate_default_products
docker compose exec -it web python manage.py populate_default_questions
docker compose exec -it web python manage.py populate_corporate_questions
docker compose exec -it web python manage.py collectstatic
docker compose exec -it web python manage.py findstatic

docker compose exec -it web python manage.py python manage.py export_questions
docker compose exec -it web python manage.py dumpdata -o data.json
docker compose exec -it cosm_web python manage.py loaddata data.json
docker compose exec -it web python manage.py loaddata users.json



docker compose exec -it web python manage.py check --deploy

# Underlining commanded
django-admin dumpdata --format=json --database=test app_label.ModelName | django-admin loaddata --format=json --database=prod -

#use after destructive db changes
docker compose exec -it web python manage.py migrate --run-syncdb
docker compose exec -it web python manage.py flush

#Postgres drop database
docker compose exec db psql -U postgres -d postgres -c "DROP DATABASE cosmetrics;"


docker exec -it <container_id/container_name> psql -U <user_name> <database_name>


docker exec -it cosm_db psql -U postgres cosmetrics

#test: [ "CMD", "pg_isready", "-q", "-d", "postgres", "-U", "postgres" ]




# Postgres Troubleshooting
psql -h localhost -U postgres



```

### Setup and run locally

To run the application locally.  At the root of the project, run the commands below in sequence:

```bash

python manage.py makemigrations
python manage.py migrate
#Not compulsory
python manage.py createsuperuser
python manage.py runserver
python manage.py populate_default_products
python manage.py populate_default_questions
python manage.py populate_corporate_questions
python manage.py collectstatic


# Clean DB
DROP DATABASE hairydb;
CREATE DATABASE hairydb;
GRANT ALL PRIVILEGES ON DATABASE hairydb TO cosops;
ALTER USER cosops CREATEDB;

```

### Postgres - Search Optimization

```python

# Optimize Postgres Search
CREATE EXTENSION IF NOT EXISTS pg_trgm;

from django.contrib.postgres.search import TrigramSimilarity
@router.get("/{str:product_name}", response=ProductOutSchema)
def get_product_by_name(request, product_name: str):
    """
    Fuzzy-matches the requested product name using trigram similarity.
    Returns the closest match above the chosen threshold (0.25 here).
    """
    product_qs = (
        Product.objects
               .annotate(similarity=TrigramSimilarity("name", product_name))
               .filter(similarity__gt=0.25)         # tweak threshold as needed
               .order_by("-similarity")              # best match first
    )

    product = product_qs.first()
    if not product:
        raise Http404("No product with a similar name was found.")
    return product




```

## Acceptance Testing

We are using Selenium library for test automation and will focus on PageObject model to ensure easy
maintenance

### Deployment Runbook (Manual steps) - Not CI/CD yet

1. Push latest code changes to Github
2. Merge code into main branch
3. Download the main repo as a zip file
4. Assuming you have the .pem from AWS EC2 creation, copy zipped repo to home directory on EC2.

Sample command:

```bash
scp -i $HOME/secrets/my_ec2_key.pem $HOME/Downloads/main_code.zip ec2-user@ec2_public_ip:~/.
```

5. Connect via ssh to AWS EC2 to finalise deployment

## Code Static Analysis

```bash
bandit -r path_to_code ```


## Test Usecases

this is based on user journeys and covers happy paths first

```bash

# Test the basic setup
python run_basic_test.py -v

# Run the full test suite
python scripts/run_users_tests.py -v

# Or run specific tests
pytest tests/users/test_basic_setup.py -v


```
