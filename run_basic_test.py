#!/usr/bin/env python3
"""
Simple test runner to verify the basic setup works.
"""

import os
import subprocess
import sys
from pathlib import Path


def main():
    """Run basic setup test."""
    print("🧪 Running Basic Setup Test")
    print("=" * 40)

    # Set up environment
    project_root = Path(__file__).parent
    os.chdir(project_root)

    # Set Django settings
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cosmetrics_ai.settings.testing")

    try:
        # Run the basic setup test
        cmd = [
            sys.executable,
            "-m",
            "pytest",
            "tests/users/test_basic_setup.py",
            "-v",
            "--tb=short",
        ]

        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=False)

        if result.returncode == 0:
            print("\n✅ Basic setup test passed!")
            print("🚀 Django configuration is working correctly.")
            return 0
        else:
            print("\n❌ Basic setup test failed!")
            print("🔧 Please check the Django configuration.")
            return 1

    except Exception as e:
        print(f"\n❌ Error running test: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
