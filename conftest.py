import os

import django
import pytest
from django.conf import settings

# Configure Django settings for tests BEFORE any Django imports
if not settings.configured:
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cosmetrics_ai.settings.testing")
    django.setup()

# Now safe to import Django components
from django.contrib.auth import get_user_model
from django.test import Client

User = get_user_model()


@pytest.fixture
def api_client():
    return Client()


@pytest.fixture
def test_user(db):
    return User.objects.create_user(email="<EMAIL>", password="pass123")
