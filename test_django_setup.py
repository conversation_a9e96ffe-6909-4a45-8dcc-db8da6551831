#!/usr/bin/env python3
"""
Test Django setup for users API testing.
"""

import os
import sys
from pathlib import Path

import django


def setup_django():
    """Set up Django for testing."""
    # Add the project root to Python path
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))

    # Set Django settings
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cosmetrics_ai.settings.testing")

    try:
        django.setup()
        print("✅ Django setup successful")
        return True
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return False


def test_imports():
    """Test importing Django components."""
    try:
        from django.contrib.auth import get_user_model

        print("✅ Django core imports successful")

        # Test users app imports
        from users.models import User

        print("✅ Users app imports successful")

        # Test user model
        User = get_user_model()
        print(f"✅ User model: {User}")

        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False


def test_database():
    """Test database operations."""
    try:
        from django.contrib.auth import get_user_model
        from django.db import connection

        # Test database connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            print(f"✅ Database connection successful: {result}")

        # Test user creation
        User = get_user_model()
        user = User.objects.create_user(email="<EMAIL>", password="testpass123")
        print(f"✅ User creation successful: {user}")

        # Clean up
        user.delete()
        print("✅ User deletion successful")

        return True
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False


def main():
    """Main test function."""
    print("🧪 Testing Django Setup for Users API")
    print("=" * 50)

    # Test Django setup
    if not setup_django():
        sys.exit(1)

    # Test imports
    if not test_imports():
        sys.exit(1)

    # Test database
    if not test_database():
        sys.exit(1)

    print("\n✅ All tests passed! Django setup is working correctly.")
    print("🚀 Ready to run users API tests!")


if __name__ == "__main__":
    main()
