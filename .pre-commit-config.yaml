# .pre-commit-config.yaml
repos:
  # ─────────── housekeeping & file‑safety ───────────
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0 # or pin the commit SHA
    hooks:
      - id: no-commit-to-branch
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-added-large-files
        args: [--maxkb=5000]
      - id: check-symlinks
      - id: check-executables-have-shebangs
      - id: detect-private-key # ← keep; *detect‑secrets* moved below
      - id: check-yaml
      - id: check-toml
      - id: mixed-line-ending
        args: [--fix=lf]
      - id: trailing-whitespace
        types: [text, python, javascript, csv]
        args: [--fix=auto]
      - id: end-of-file-fixer
      - id: fix-byte-order-marker

  # ─────────── secret scanning (NEW repo) ───────────
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.5.0
    hooks:
      - id: detect-secrets
        args: ['--baseline', '.secrets.baseline'] # optional but recommended

  # ─────────── Ruff: lint‑&‑fix ➜ format ───────────
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.12.4
    hooks:
      # 1️⃣  lint, auto‑fix, and sort imports
      - id: ruff
        name: ruff (lint, fix, sort-imports)
        args: [
            '--select=E,W,F,B,I,UP', # I = import‑sort rules
            '--ignore=D,ERA',
            '--fix',
            '--exit-non-zero-on-fix',
            '--target-version=py312',
            '--line-length=120',
            '--per-file-ignores=__init__.py:F401'
          ]
        types_or: [python, pyi]

      # 2️⃣  formatter (never re‑orders imports)
      - id: ruff-format
        name: ruff (format)
        args: ['--line-length=120']
        types_or: [python, pyi]

  # ─────────── Django-specific checks ───────────
  - repo: local
    hooks:
      # Django system checks
      - id: django-check
        name: Django Check
        entry: python manage.py check
        language: system
        pass_filenames: false
        files: \.py$
        exclude: ^(venv/|env/|node_modules/)

      # Django migration checks
      - id: django-check-migrations
        name: Django Check Migrations
        entry: python manage.py makemigrations --check --dry-run
        language: system
        pass_filenames: false
        files: \.py$
        exclude: ^(venv/|env/|migrations/|node_modules/)

  # ─────────── Security scanning with bandit ───────────
  - repo: https://github.com/pycqa/bandit
    rev: 1.8.6
    hooks:
      - id: bandit
        args: [-r, ., -f, json, -o, bandit-report.json, --skip, B101, B601]
        exclude: ^(tests/|venv/|env/|node_modules/|migrations/)

  # ─────────── Custom security validation ───────────
  - repo: local
    hooks:
      - id: security-validation
        name: Security Validation
        entry: python scripts/validate_security.py
        language: system
        pass_filenames: false
        files: \.(py|js|ts|tsx|json|yaml|yml)$
        exclude: ^(venv/|env/|node_modules/|migrations/)

  # ─────────── Type checking with mypy ───────────
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.17.0
    hooks:
      - id: mypy
        additional_dependencies: [django-stubs, types-requests]
        exclude: ^(migrations/|venv/|env/|tests/|node_modules/)
        args: [--ignore-missing-imports, --no-strict-optional]
