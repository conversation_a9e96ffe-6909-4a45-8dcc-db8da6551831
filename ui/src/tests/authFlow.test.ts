/**
 * Authentication Flow Test
 * 
 * This test validates that the React Query optimization fixes work correctly
 * and prevents user data leakage between sessions.
 */

import { queryClient, apiClient } from '../services/modernAPI';
import { store } from '../redux/store';
import { resetQuiz, setSessionGuid, updateQuizAnswers } from '../redux/QuizSlice';

// Mock user data for testing
const mockUser1 = {
  id: 1,
  email: '<EMAIL>',
  first_name: 'User',
  last_name: 'One',
  full_name: 'User One',
  is_active: true,
  date_joined: '2024-01-01T00:00:00Z'
};

const mockUser2 = {
  id: 2,
  email: '<EMAIL>',
  first_name: 'User',
  last_name: 'Two',
  full_name: 'User Two',
  is_active: true,
  date_joined: '2024-01-02T00:00:00Z'
};

const mockTokenResponse1 = {
  access: 'mock_access_token_1',
  refresh: 'mock_refresh_token_1',
  user: mockUser1,
  success: true
};

const mockTokenResponse2 = {
  access: 'mock_access_token_2',
  refresh: 'mock_refresh_token_2',
  user: mockUser2,
  success: true
};

/**
 * Test function to simulate user login and check for data leakage
 */
export function testAuthenticationFlow() {
  console.log('🧪 Starting Authentication Flow Test...');

  // Step 1: Clear everything to start fresh
  console.log('Step 1: Clearing all data...');
  queryClient.clear();
  apiClient.clearAllTokens();
  localStorage.clear();
  store.dispatch(resetQuiz());

  // Step 2: Simulate User 1 login
  console.log('Step 2: Simulating User 1 login...');

  // Set User 1 data in cache
  queryClient.setQueryData(['user'], mockUser1);
  queryClient.setQueryData(['userProfile', mockUser1.id], {
    id: 1,
    user_id: mockUser1.id,
    first_name: mockUser1.first_name,
    last_name: mockUser1.last_name,
    completed_questions: 10,
    questionaire_completed: true
  });

  // Set User 1 quiz state
  store.dispatch(setSessionGuid('user1-session-guid'));
  store.dispatch(updateQuizAnswers({ '1': 'answer1', '2': 'answer2' }));
  localStorage.setItem('continueSessionGuid', 'user1-session-guid');
  localStorage.setItem('preservedQuizSession', JSON.stringify({
    session_guid: 'user1-session-guid',
    answers: { '1': 'answer1', '2': 'answer2' },
    timestamp: new Date().toISOString(),
    step: 3
  }));

  // Set User 1 tokens
  localStorage.setItem('auth', JSON.stringify(mockTokenResponse1));
  localStorage.setItem('access_token', mockTokenResponse1.access);
  localStorage.setItem('refresh_token', mockTokenResponse1.refresh);

  console.log('User 1 data set. Cache contents:');
  console.log('- User:', queryClient.getQueryData(['user']));
  console.log('- User Profile:', queryClient.getQueryData(['userProfile', mockUser1.id]));
  console.log('- Quiz State:', store.getState().quiz);
  console.log('- Quiz localStorage:', {
    continueSessionGuid: localStorage.getItem('continueSessionGuid'),
    preservedQuizSession: localStorage.getItem('preservedQuizSession')
  });
  console.log('- Token Status:', apiClient.getTokenStatus());

  // Step 3: Simulate User 1 logout
  console.log('Step 3: Simulating User 1 logout...');
  queryClient.clear();
  apiClient.clearAllTokens();
  store.dispatch(resetQuiz());
  localStorage.removeItem('continueSessionGuid');
  localStorage.removeItem('preservedQuizSession');

  // Step 4: Simulate User 2 login (this should not see User 1's data)
  console.log('Step 4: Simulating User 2 login...');

  // This simulates what happens in the optimized login flow
  queryClient.clear(); // This is the key fix we added
  store.dispatch(resetQuiz()); // Clear quiz state
  localStorage.removeItem('continueSessionGuid');
  localStorage.removeItem('preservedQuizSession');

  // Set User 2 data
  queryClient.setQueryData(['user'], mockUser2);
  localStorage.setItem('auth', JSON.stringify(mockTokenResponse2));
  localStorage.setItem('access_token', mockTokenResponse2.access);
  localStorage.setItem('refresh_token', mockTokenResponse2.refresh);

  // Step 5: Check for data leakage
  console.log('Step 5: Checking for data leakage...');

  const currentUser = queryClient.getQueryData(['user']) as typeof mockUser2 | undefined;
  const user1Profile = queryClient.getQueryData(['userProfile', mockUser1.id]);
  const user2Profile = queryClient.getQueryData(['userProfile', mockUser2.id]);
  const quizState = store.getState().quiz;
  const continueSessionGuid = localStorage.getItem('continueSessionGuid');
  const preservedQuizSession = localStorage.getItem('preservedQuizSession');

  console.log('Current user after User 2 login:', currentUser);
  console.log('User 1 profile (should be undefined):', user1Profile);
  console.log('User 2 profile (should be undefined initially):', user2Profile);
  console.log('Quiz state (should be empty):', quizState);
  console.log('Quiz localStorage (should be null):', { continueSessionGuid, preservedQuizSession });

  // Validation
  const isValid = (
    currentUser?.id === mockUser2.id &&
    user1Profile === undefined &&
    user2Profile === undefined &&
    Object.keys(quizState).length === 0 &&
    continueSessionGuid === null &&
    preservedQuizSession === null
  );

  if (isValid) {
    console.log('✅ Test PASSED: No data leakage detected!');
    console.log('- Current user is User 2');
    console.log('- User 1 profile data was properly cleared');
    console.log('- Quiz state was properly cleared');
    console.log('- Quiz localStorage was properly cleared');
    console.log('- No stale cache data found');
  } else {
    console.log('❌ Test FAILED: Data leakage detected!');
    console.log('- Current user:', currentUser);
    console.log('- User 1 profile should be undefined but is:', user1Profile);
    console.log('- Quiz state should be empty but is:', quizState);
    console.log('- Quiz localStorage should be null but is:', { continueSessionGuid, preservedQuizSession });
  }

  // Step 6: Clean up
  console.log('Step 6: Cleaning up...');
  queryClient.clear();
  apiClient.clearAllTokens();
  localStorage.clear();
  store.dispatch(resetQuiz());

  console.log('🧪 Authentication Flow Test Complete');
  return isValid;
}

/**
 * Test function to validate token synchronization between systems
 */
export function testTokenSynchronization() {
  console.log('🔄 Starting Token Synchronization Test...');

  // Clear everything first
  queryClient.clear();
  apiClient.clearAllTokens();
  localStorage.clear();

  // Test 1: Set legacy tokens and check modern API client picks them up
  console.log('Test 1: Legacy to Modern sync...');
  localStorage.setItem('auth', JSON.stringify(mockTokenResponse1));

  // Force reload
  apiClient.reloadTokensFromStorage();

  const tokenStatus1 = apiClient.getTokenStatus();
  console.log('Token status after legacy set:', tokenStatus1);

  const hasModernTokens1 = localStorage.getItem('access_token') && localStorage.getItem('refresh_token');
  console.log('Modern tokens created:', !!hasModernTokens1);

  // Test 2: Set modern tokens and check legacy format is updated
  console.log('Test 2: Modern to Legacy sync...');
  localStorage.clear();
  localStorage.setItem('access_token', mockTokenResponse2.access);
  localStorage.setItem('refresh_token', mockTokenResponse2.refresh);

  apiClient.reloadTokensFromStorage();

  const legacyAuth = localStorage.getItem('auth');
  console.log('Legacy auth after modern set:', legacyAuth);

  const isValid = tokenStatus1.hasAccess && tokenStatus1.hasRefresh && hasModernTokens1 && legacyAuth;

  if (isValid) {
    console.log('✅ Token Synchronization Test PASSED!');
  } else {
    console.log('❌ Token Synchronization Test FAILED!');
  }

  // Clean up
  queryClient.clear();
  apiClient.clearAllTokens();
  localStorage.clear();

  console.log('🔄 Token Synchronization Test Complete');
  return isValid;
}

// Export a function to run all tests
export function runAllAuthTests() {
  console.log('🚀 Running All Authentication Tests...');

  const test1Result = testAuthenticationFlow();
  const test2Result = testTokenSynchronization();

  const allPassed = test1Result && test2Result;

  console.log('\n📊 Test Results Summary:');
  console.log(`Authentication Flow: ${test1Result ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Token Synchronization: ${test2Result ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Overall: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

  return allPassed;
}
