import React from 'react';
import { useABTest } from '../../hooks/useABTest';
import { isTestEnabled, getForcedVariant, getTestConfig } from '../../config/abTestConfig';
import Home from './index'; // Current home page (Variant A)
import Layout from '../Layout';
import EditorialLanding from './EditorialLanding'; // New editorial design (Variant B)

/**
 * A/B Test wrapper for landing page variants
 * 
 * Variant A: Current home page design
 * Variant B: New editorial design
 * 
 * Traffic split: 50/50
 */
const ABTestLanding: React.FC = () => {
  // Always call hooks at the top level
  const testEnabled = isTestEnabled('landing_page_redesign');
  const forcedVariant = getForcedVariant('landing_page_redesign');
  const testConfig = getTestConfig('landing_page_redesign');

  // Always call useABTest hook (required by React Hooks rules)
  const { variant, isLoading, trackEvent } = useABTest({
    testName: 'landing_page_redesign',
    variants: ['A', 'B'],
    weights: testConfig?.weights || [0.5, 0.5]
  });

  // Track when user starts quiz (conversion event) - MUST be at top level
  React.useEffect(() => {
    const handleQuizStart = () => {
      trackEvent('conversion', {
        conversionType: 'quiz_start',
        variant,
        timestamp: new Date().toISOString()
      });
    };

    // Listen for quiz start events
    window.addEventListener('quiz_started', handleQuizStart);

    return () => {
      window.removeEventListener('quiz_started', handleQuizStart);
    };
  }, [variant, trackEvent]);

  // Check configuration-based overrides after hooks are called
  if (!testEnabled) {
    console.log('🛑 A/B Test disabled via configuration - showing default variant');
    return <Home />;
  }

  if (forcedVariant) {
    console.log(`🎯 A/B Test forced to variant ${forcedVariant} via configuration`);
    if (forcedVariant === 'B') {
      return <EditorialLanding />;
    }
    return <Home />;
  }

  if (!testConfig) {
    console.warn('⚠️ A/B Test configuration not found - showing default variant');
    return <Home />;
  }

  // Track CTA clicks for conversion analysis
  const handleCTAClick = () => {
    trackEvent('cta_click', {
      buttonText: variant === 'A' ? 'Get Started Now' : 'Start Analysis',
      location: 'hero_section'
    });
  };

  const handleSecondaryClick = () => {
    trackEvent('secondary_cta_click', {
      buttonText: variant === 'A' ? 'Learn More' : 'Watch Demo',
      location: 'hero_section'
    });
  };

  // Show loading state while determining variant
  if (isLoading) {
    return (
      <Layout>
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '50vh',
          color: '#6B7280'
        }}>
          Loading...
        </div>
      </Layout>
    );
  }

  // Render based on assigned variant
  if (variant === 'B') {
    // Editorial design (Variant B)
    return (
      <Layout>
        {/* Development indicator - remove in production */}
        {process.env.NODE_ENV === 'development' && (
          <div style={{
            background: '#1F2937',
            color: 'white',
            padding: '0.5rem',
            textAlign: 'center',
            fontSize: '0.875rem',
            fontWeight: '600'
          }}>
            A/B Test: Variant B (Editorial Design) - 50% Traffic
          </div>
        )}

        <EditorialLanding
          onCTAClick={handleCTAClick}
          onSecondaryClick={handleSecondaryClick}
        />
      </Layout>
    );
  }

  // Current design (Variant A) - default
  return (
    <>
      {/* Development indicator - remove in production */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{
          background: '#4300FF',
          color: 'white',
          padding: '0.5rem',
          textAlign: 'center',
          fontSize: '0.875rem',
          fontWeight: '600'
        }}>
          A/B Test: Variant A (Current Design) - 50% Traffic
        </div>
      )}

      <Home />
    </>
  );
};

export default ABTestLanding;
