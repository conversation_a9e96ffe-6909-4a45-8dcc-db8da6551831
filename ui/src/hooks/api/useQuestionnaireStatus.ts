import { useQuery } from "@tanstack/react-query";
import api from "../../api";
import { validateUserId, logUserAuthState, getCurrentUserId } from "../../utils/userValidation";

export interface QuestionnaireStatus {
  questionaire_completed: boolean;
  completed_questions: number;
  total_questions: number;
  status: 'NotStarted' | 'Started' | 'Completed';
}

export const useQuestionnaireStatus = (userId?: number) => {
  return useQuery<QuestionnaireStatus>({
    queryKey: ["questionnaireStatus", userId],
    queryFn: async () => {
      // Enhanced user ID validation
      if (!userId) {
        logUserAuthState('useQuestionnaireStatus - No userId provided');
        const currentUser = getCurrentUserId();
        if (currentUser.isValid && currentUser.userId) {
          userId = currentUser.userId;
          console.log(`🔄 Using current user ID from ${currentUser.source}: ${userId}`);
        } else {
          throw new Error(`User ID is required. ${currentUser.error || 'No valid user found'}`);
        }
      }

      // Validate the user ID
      const validation = validateUserId(userId);
      if (!validation.isValid) {
        throw new Error(`Invalid user ID: ${validation.error}`);
      }

      console.log(`🔍 Fetching questionnaire status for user ID: ${userId}`);

      try {
        // Try to get user profile with questionnaire status
        console.log(`📡 Making API call to: /api/users/${userId}`);
        const response = await api.get(`/users/${userId}`);
        const profile = response.data;
        console.log(`✅ Profile data received:`, profile);

        // Get total questions count
        console.log(`📡 Making API call to: /api/quiz/questions/count`);
        const questionsResponse = await api.get('/quiz/questions/count');
        const totalQuestions = questionsResponse.data.count || 0;
        console.log(`✅ Total questions count: ${totalQuestions}`);

        const completedQuestions = profile.completed_questions || 0;
        const questionaireCompleted = profile.questionaire_completed || false;

        let status: 'NotStarted' | 'Started' | 'Completed' = 'NotStarted';

        if (completedQuestions === 0) {
          status = 'NotStarted';
        } else if (completedQuestions >= totalQuestions || questionaireCompleted) {
          status = 'Completed';
        } else {
          status = 'Started';
        }

        const result = {
          questionaire_completed: questionaireCompleted,
          completed_questions: completedQuestions,
          total_questions: totalQuestions,
          status
        };

        console.log(`✅ Questionnaire status result:`, result);
        return result;
      } catch (error: any) {
        console.error("❌ Error fetching questionnaire status:", error);

        // Enhanced error logging
        if (error.response) {
          console.error(`❌ API Error Details:`, {
            status: error.response.status,
            statusText: error.response.statusText,
            url: error.config?.url,
            method: error.config?.method,
            data: error.response.data
          });

          // Handle specific error cases
          if (error.response.status === 404) {
            console.warn(`⚠️ User profile not found for ID ${userId}. This might be a new user or invalid ID.`);
          } else if (error.response.status === 401) {
            console.warn(`⚠️ Unauthorized access. User might need to log in again.`);
          } else if (error.response.status >= 500) {
            console.warn(`⚠️ Server error. Backend might be experiencing issues.`);
          }
        } else if (error.request) {
          console.error(`❌ Network Error:`, {
            message: 'No response received from server',
            url: error.config?.url,
            timeout: error.config?.timeout
          });
        } else {
          console.error(`❌ Request Setup Error:`, error.message);
        }

        // Return default values if API fails
        const defaultResult = {
          questionaire_completed: false,
          completed_questions: 0,
          total_questions: 0,
          status: 'NotStarted' as const
        };

        console.log(`🔄 Returning default questionnaire status:`, defaultResult);
        return defaultResult;
      }
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 404 (user not found) or 401 (unauthorized)
      if (error?.response?.status === 404 || error?.response?.status === 401) {
        console.log(`🚫 Not retrying due to ${error.response.status} error`);
        return false;
      }

      // Retry up to 2 times for other errors
      const shouldRetry = failureCount < 2;
      console.log(`🔄 Retry attempt ${failureCount + 1}/3 for questionnaire status: ${shouldRetry}`);
      return shouldRetry;
    },
    retryDelay: (attemptIndex) => {
      const delay = Math.min(1000 * Math.pow(2, attemptIndex), 5000);
      console.log(`⏱️ Retrying questionnaire status in ${delay}ms`);
      return delay;
    },
  });
};
