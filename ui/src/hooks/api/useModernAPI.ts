/**
 * Modern React hooks for API integration
 * 
 * Provides type-safe, cached, and optimized data fetching
 * using React Query and our modern API client.
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  apiClient,
  User,
  UserProfile,
  Product,
  Recommendation,
  HairScoreReport,
  LoginRequest,
  RegisterRequest,
  APIResponse
} from '../../services/modernAPI';
import { store } from '../../redux/store';
import { resetQuiz } from '../../redux/QuizSlice';

// Query Keys
export const QUERY_KEYS = {
  user: ['user'] as const,
  userProfile: (userId: number) => ['user', 'profile', userId] as const,
  products: (filters?: any) => ['products', filters] as const,
  product: (id: number) => ['product', id] as const,
  recommendations: (userId?: number) => ['recommendations', userId] as const,
  hairScores: (userId: number, sessionGuid?: string) => ['hairScores', userId, sessionGuid] as const,
  questionnaires: ['questionnaires'] as const,
} as const;

// Authentication Hooks
export function useLogin() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (credentials: LoginRequest) => apiClient.login(credentials),
    onSuccess: (data) => {
      if (data.success && data.user) {
        // CRITICAL: Clear ALL cached data first to prevent previous user's data from showing
        // This ensures no data leakage between user sessions
        queryClient.clear();

        // Clear Redux quiz state to prevent quiz completion issues
        store.dispatch(resetQuiz());

        // Clear localStorage quiz-related data
        localStorage.removeItem('continueSessionGuid');
        localStorage.removeItem('preservedQuizSession');
        localStorage.removeItem('quizSessionGuid');

        // Cache new user data
        queryClient.setQueryData(QUERY_KEYS.user, data.user);

        // Invalidate and refetch user-related queries (redundant after clear, but good for clarity)
        queryClient.invalidateQueries({ queryKey: ['user'] });
      }
    },
    onError: (error) => {
      console.error('Login failed:', error);
    },
  });
}

export function useRegister() {
  return useMutation({
    mutationFn: (userData: RegisterRequest) => apiClient.register(userData),
    onError: (error) => {
      console.error('Registration failed:', error);
    },
  });
}

export function useLogout() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => apiClient.logout(),
    onSuccess: () => {
      // Clear all cached data
      queryClient.clear();

      // Clear quiz state and related localStorage data
      store.dispatch(resetQuiz());
      localStorage.removeItem('continueSessionGuid');
      localStorage.removeItem('preservedQuizSession');
      localStorage.removeItem('quizSessionGuid');
    },
    onError: (error) => {
      console.error('Logout failed:', error);
      // Clear cache anyway
      queryClient.clear();

      // Clear quiz state even on error
      store.dispatch(resetQuiz());
      localStorage.removeItem('continueSessionGuid');
      localStorage.removeItem('preservedQuizSession');
      localStorage.removeItem('quizSessionGuid');
    },
  });
}

// User Hooks
export function useCurrentUser() {
  return useQuery({
    queryKey: QUERY_KEYS.user,
    queryFn: () => apiClient.getCurrentUser(),
    enabled: apiClient.isAuthenticated(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry if user is not authenticated
      if (error instanceof Error && error.message.includes('401')) {
        return false;
      }
      return failureCount < 2;
    },
  });
}

export function useUserProfile(userId: number) {
  return useQuery({
    queryKey: QUERY_KEYS.userProfile(userId),
    queryFn: async () => {
      console.log(`🔍 Fetching user profile for ID: ${userId}`);
      try {
        const profile = await apiClient.getUserProfile(userId);
        console.log(`✅ User profile fetched successfully:`, profile);
        return profile;
      } catch (error: any) {
        console.error(`❌ Error fetching user profile for ID ${userId}:`, error);

        if (error.message?.includes('404')) {
          console.warn(`⚠️ User profile not found for ID ${userId}. This might be a new user or invalid ID.`);
        }

        throw error; // Re-throw to let React Query handle it
      }
    },
    enabled: !!userId && apiClient.isAuthenticated(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 404 (user not found) or 401 (unauthorized)
      if (error?.message?.includes('404') || error?.message?.includes('401')) {
        console.log(`🚫 Not retrying user profile due to ${error.message.includes('404') ? '404' : '401'} error`);
        return false;
      }

      // Retry up to 2 times for other errors
      const shouldRetry = failureCount < 2;
      console.log(`🔄 Retry attempt ${failureCount + 1}/3 for user profile: ${shouldRetry}`);
      return shouldRetry;
    },
  });
}

export function useUpdateUserProfile(userId: number) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Partial<UserProfile>) => apiClient.updateUserProfile(userId, data),
    onSuccess: (updatedProfile) => {
      // Update cached profile data
      queryClient.setQueryData(QUERY_KEYS.userProfile(userId), updatedProfile);
      // Invalidate user data to refresh
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.user });
    },
    onError: (error) => {
      console.error('Profile update failed:', error);
    },
  });
}

// Product Hooks
export function useProducts(filters?: {
  category?: string;
  hairtype?: string;
  porosity?: string;
  price_min?: number;
  price_max?: number;
  page?: number;
  page_size?: number;
}) {
  return useQuery({
    queryKey: QUERY_KEYS.products(filters),
    queryFn: () => apiClient.getProducts(filters),
    staleTime: 15 * 60 * 1000, // 15 minutes - products don't change often
    placeholderData: (previousData) => previousData, // Keep previous data while fetching new data
  });
}

export function useProduct(productId: number) {
  return useQuery({
    queryKey: QUERY_KEYS.product(productId),
    queryFn: () => apiClient.getProduct(productId),
    enabled: !!productId,
    staleTime: 30 * 60 * 1000, // 30 minutes - individual products are stable
  });
}

// Recommendation Hooks
export function useRecommendations(userId?: number) {
  return useQuery({
    queryKey: QUERY_KEYS.recommendations(userId),
    queryFn: () => apiClient.getRecommendations(userId),
    enabled: apiClient.isAuthenticated(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useGenerateRecommendations() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userId, preferences }: { userId: number; preferences?: any; }) =>
      apiClient.generateRecommendations(userId, preferences),
    onSuccess: (newRecommendation, variables) => {
      // Invalidate recommendations to refetch
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.recommendations(variables.userId)
      });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.recommendations()
      });
    },
    onError: (error) => {
      console.error('Recommendation generation failed:', error);
    },
  });
}

// Hair Score Hooks
export function useHairScores(userId: number, sessionGuid?: string) {
  return useQuery({
    queryKey: QUERY_KEYS.hairScores(userId, sessionGuid),
    queryFn: () => apiClient.getHairScores(userId, sessionGuid),
    enabled: !!userId && apiClient.isAuthenticated(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Quiz Hooks
export function useQuestionnaires() {
  return useQuery({
    queryKey: QUERY_KEYS.questionnaires,
    queryFn: () => apiClient.getQuestionnaires(),
    staleTime: 60 * 60 * 1000, // 1 hour - questionnaires are stable
  });
}

export function useSubmitQuizResponse() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => apiClient.submitQuizResponse(data),
    onSuccess: () => {
      // Invalidate user profile and recommendations after quiz completion
      queryClient.invalidateQueries({ queryKey: ['user'] });
      queryClient.invalidateQueries({ queryKey: ['recommendations'] });
      queryClient.invalidateQueries({ queryKey: ['hairScores'] });
    },
    onError: (error) => {
      console.error('Quiz submission failed:', error);
    },
  });
}

// Quiz-specific Recommendation Hooks
export function useGenerateQuizRecommendations() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userId, sessionGuid }: { userId: number; sessionGuid: string; }) =>
      apiClient.generateQuizRecommendations(userId, sessionGuid),
    onSuccess: (data, variables) => {
      console.log('Quiz recommendations generated successfully:', data);
      // Invalidate and refetch recommendations
      queryClient.invalidateQueries({ queryKey: ['recommendations', variables.userId] });
      queryClient.invalidateQueries({ queryKey: ['recommendations', variables.userId, variables.sessionGuid] });
      // Also invalidate the general recommendations query
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.recommendations(variables.userId) });
    },
    onError: (error) => {
      console.error('Quiz recommendation generation failed:', error);
    },
  });
}

export function useUserRecommendations(userId: number) {
  return useQuery({
    queryKey: ['recommendations', userId],
    queryFn: () => apiClient.getUserRecommendations(userId),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useRecommendationBySession(userId: number, sessionGuid: string) {
  return useQuery({
    queryKey: ['recommendations', userId, sessionGuid],
    queryFn: () => apiClient.getRecommendationBySession(userId, sessionGuid),
    enabled: !!userId && !!sessionGuid,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Quiz Session Management Hooks
export function useDeleteQuizSession() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (sessionGuid: string) => apiClient.deleteQuizSession(sessionGuid),
    onSuccess: (data, sessionGuid) => {
      console.log('Quiz session deleted successfully:', sessionGuid);
      // Invalidate user sessions queries to refresh the list
      queryClient.invalidateQueries({ queryKey: ['userSessions'] });
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
    },
    onError: (error) => {
      console.error('Quiz session deletion failed:', error);
    },
  });
}

// Utility Hooks
export function useAuthStatus() {
  const { data: user, isLoading, error } = useCurrentUser();

  return {
    isAuthenticated: apiClient.isAuthenticated() && !!user,
    user,
    isLoading,
    error,
  };
}

export function useApiHealth() {
  return useQuery({
    queryKey: ['health'],
    queryFn: () => apiClient.healthCheck(),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    retry: 1,
  });
}

// Prefetch Hooks for Performance
export function usePrefetchUserData(userId: number) {
  const queryClient = useQueryClient();

  const prefetchProfile = () => {
    queryClient.prefetchQuery({
      queryKey: QUERY_KEYS.userProfile(userId),
      queryFn: () => apiClient.getUserProfile(userId),
      staleTime: 5 * 60 * 1000,
    });
  };

  const prefetchRecommendations = () => {
    queryClient.prefetchQuery({
      queryKey: QUERY_KEYS.recommendations(userId),
      queryFn: () => apiClient.getRecommendations(userId),
      staleTime: 10 * 60 * 1000,
    });
  };

  const prefetchHairScores = () => {
    queryClient.prefetchQuery({
      queryKey: QUERY_KEYS.hairScores(userId),
      queryFn: () => apiClient.getHairScores(userId),
      staleTime: 5 * 60 * 1000,
    });
  };

  return {
    prefetchProfile,
    prefetchRecommendations,
    prefetchHairScores,
    prefetchAll: () => {
      prefetchProfile();
      prefetchRecommendations();
      prefetchHairScores();
    },
  };
}

// Error Handling Hook
export function useAPIError() {
  const queryClient = useQueryClient();

  const handleError = (error: Error) => {
    console.error('API Error:', error);

    // If it's an authentication error, clear user data
    if (error.message.includes('401') || error.message.includes('Authentication failed')) {
      queryClient.setQueryData(QUERY_KEYS.user, null);
      queryClient.clear();
    }
  };

  return { handleError };
}

export default {
  useLogin,
  useRegister,
  useLogout,
  useCurrentUser,
  useUserProfile,
  useUpdateUserProfile,
  useProducts,
  useProduct,
  useRecommendations,
  useGenerateRecommendations,
  useGenerateQuizRecommendations,
  useUserRecommendations,
  useRecommendationBySession,
  useDeleteQuizSession,
  useHairScores,
  useQuestionnaires,
  useSubmitQuizResponse,
  useAuthStatus,
  useApiHealth,
  usePrefetchUserData,
  useAPIError,
};
