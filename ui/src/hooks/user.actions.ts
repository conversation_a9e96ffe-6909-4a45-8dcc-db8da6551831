// useUserActions.ts
import api from "../api";
import { toast } from "react-toastify";
import { store } from "../redux/store";
import { reduxLogin, reduxLogout } from "../redux/authSlice";
import { updateUser } from "../redux/UserSlice";
import { handleApiError, showSuccessMessage } from "../services/errorHandler";
import { generateRecommendations } from "../apis";
import { logError } from "../utils/errorHandling";
import { queryClient, apiClient } from "../services/modernAPI";

// Token and user data types
interface TokenResponse {
  access: string;
  refresh: string;
  message?: string;
  success?: boolean;
  user: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
  };
}

// JWT-based authentication - no CSRF tokens needed

// JWT-based authentication - CSRF token not needed

// Helper function to handle quiz session attribution after login/signup
async function handleQuizSessionAttribution(userId: number) {
  try {
    const preservedSession = localStorage.getItem('preservedQuizSession');
    if (!preservedSession) {
      console.log('No preserved quiz session found');
      return;
    }

    const session = JSON.parse(preservedSession);
    console.log('Attributing quiz session to user:', { userId, sessionGuid: session.session_guid });

    // Attribute the session to the user
    await api.post('/api/quiz/replies/attribute-session', {
      session_guid: session.session_guid,
      user_id: userId
    });

    console.log('Quiz session successfully attributed to user');

    // Generate recommendations for the attributed session
    try {
      console.log('Generating recommendations for attributed session...');
      await generateRecommendations(userId.toString(), session.session_guid);
      console.log('Recommendations generated for attributed session');
    } catch (recommendationError) {
      logError(recommendationError, 'session_attribution_recommendation_generation', {
        userId,
        sessionGuid: session.session_guid
      });
      // Don't fail the attribution process if recommendation generation fails
    }

    // Clean up preserved session
    localStorage.removeItem('preservedQuizSession');

    // Show success message
    toast.success('Your quiz results have been saved to your account!');

  } catch (error: any) {
    console.error('Error attributing quiz session:', error);
    // Don't show error to user as this is a background operation
    // The quiz session will remain preserved for manual recovery
  }
}

// Auth-related actions
export function useUserActions(navigate: (path: string) => void) {
  return {
    signup,
    login,
    logout,
    isLoggedIn,
    refreshAccessToken,
  };

  async function signup(data: {
    username: string;
    email: string;
    password: string;
  }) {
    try {
      const response = await api.post<TokenResponse>(`/users/register`, data);
      const res = response.data;

      if (res?.success) {
        // If user has access token (auto-login after signup), handle quiz session
        if (res?.access && res?.user?.id) {
          setUserData(res);
          store.dispatch(updateUser({ user: res.user }));

          // Handle quiz session attribution
          await handleQuizSessionAttribution(res.user.id);

          showSuccessMessage("Account created successfully! Redirecting...");

          // Check for redirect parameter
          const urlParams = new URLSearchParams(window.location.search);
          const redirect = urlParams.get('redirect');

          setTimeout(() => {
            if (redirect) {
              navigate(redirect);
            } else {
              navigate("/dashboard");
            }
          }, 1500);
        } else {
          // Traditional signup flow - redirect to login
          showSuccessMessage("Signup successful! Please login.");
          setTimeout(() => navigate("/login"), 2000);
        }
      } else {
        toast.error(res?.message || "Signup failed.");
      }

      return res;
    } catch (error: any) {
      handleApiError("Signup failed", error, {
        action: 'signup',
        component: 'useUserActions'
      });
      return null;
    }
  }

  async function login(data: { email: string; password: string; }) {
    try {
      // CRITICAL: Clear ALL cached data first to prevent previous user's data from showing
      // This ensures no data leakage between user sessions
      queryClient.clear();

      // Since API has CSRF disabled, we don't need CSRF token
      const response = await api.post<TokenResponse>(`/users/login`, data);

      const res = response.data;

      if (res?.access && res?.refresh) {
        setUserData(res);
        store.dispatch(updateUser({ user: res.user }));

        // Handle quiz session attribution
        await handleQuizSessionAttribution(res.user.id);

        showSuccessMessage("Login successful!");

        // Check for redirect parameter
        const urlParams = new URLSearchParams(window.location.search);
        const redirect = urlParams.get('redirect');

        if (redirect) {
          navigate(redirect);
        } else {
          navigate("/dashboard");
        }
      } else {
        toast.error(res?.message || "Login failed.");
      }

      return res;
    } catch (error: any) {
      handleApiError("Login failed", error, {
        action: 'login',
        component: 'useUserActions',
        userId: data.email
      });
      return null;
    }
  }

  function logout() {
    // Clear legacy authentication data
    localStorage.removeItem("auth");
    store.dispatch(reduxLogout());
    store.dispatch(updateUser({ user: {} }));

    // Clear modern API client tokens and cache
    apiClient.clearAllTokens();
    queryClient.clear();

    showSuccessMessage("Logout successful!");
    navigate("/");
  }

  function isLoggedIn(): boolean {
    try {
      const auth = JSON.parse(localStorage.getItem("auth") || "{}");
      return !!auth.access && !!auth.user;
    } catch (error) {
      console.error("Failed to parse local storage auth:", error);
      return false;
    }
  }

  async function refreshAccessToken() {
    const refreshToken = getRefreshToken();

    if (!refreshToken) {
      logout();
      return;
    }

    try {
      const response = await api.post(`/token/refresh`, {
        refresh: refreshToken,
      });
      const newAccess = response.data?.access;

      if (newAccess) {
        const auth = JSON.parse(localStorage.getItem("auth") || "{}");
        auth.access = newAccess;
        localStorage.setItem("auth", JSON.stringify(auth));
      } else {
        logout();
      }
    } catch (error: any) {
      handleApiError("Token refresh failed", error, {
        action: 'token_refresh',
        component: 'useUserActions'
      });
      logout();
    }
  }

  function setUserData(data: TokenResponse) {
    localStorage.setItem(
      "auth",
      JSON.stringify({
        access: data.access,
        refresh: data.refresh,
        user: data.user,
      })
    );
    store.dispatch(reduxLogin(data.access));

    // Sync tokens with modern API client
    localStorage.setItem('access_token', data.access);
    localStorage.setItem('refresh_token', data.refresh);

    // Force modern API client to reload tokens
    apiClient.reloadTokensFromStorage();
  }

  function getRefreshToken(): string | null {
    try {
      const auth = JSON.parse(localStorage.getItem("auth") || "{}");
      return auth.refresh || null;
    } catch (error) {
      console.error("Error parsing refresh token from local storage:", error);
      return null;
    }
  }
}

// Upload questionnaire response
export const uploadQuestionaireReply = async (
  user_id: string,
  question_id: string,
  question_response: string,
  csrfToken: string,
  sessionGuid: string
) => {
  try {
    const parsedQuestionId = parseInt(question_id);
    if (isNaN(parsedQuestionId)) return;

    const responses = Array.isArray(question_response)
      ? question_response
      : [question_response];

    const cleanedResponses = Array.from(
      new Set(responses.filter((r) => r?.trim()))
    );

    const responsePromises = cleanedResponses.map((reply) =>
      api
        .post(
          `/quiz/replies`,
          {
            text: reply,
            question_id: parsedQuestionId,
            user_id,
            session_guid: sessionGuid,
          },
          {
            // No CSRF token needed for JWT-based authentication
          }
        )
        .catch((error) => {
          handleApiError("Error uploading reply", error, {
            action: 'upload_reply',
            component: 'uploadQuestionaireReply'
          });
          return null;
        })
    );

    const results = await Promise.all(responsePromises);
    return results.filter(Boolean);
  } catch (error) {
    handleApiError("Error in uploadQuestionaireReply", error, {
      action: 'upload_questionnaire_reply',
      component: 'uploadQuestionaireReply'
    });
    return;
  }
};

// Upload questionnaire images
export const uploadQuestionaireReplyImages = async (
  formData: FormData,
  csrfToken: string
) => {
  try {
    return await api.post(`/quiz/replies/images`, formData, {
      headers: {
        // No CSRF token needed for JWT-based authentication
        "Content-Type": "multipart/form-data",
      },
    });
  } catch (error) {
    handleApiError("Image upload failed", error, {
      action: 'upload_image',
      component: 'uploadQuestionaireReplyImages'
    });
    return;
  }
};

// Old centralized error handler removed - now using unified error handler from services/errorHandler.ts
