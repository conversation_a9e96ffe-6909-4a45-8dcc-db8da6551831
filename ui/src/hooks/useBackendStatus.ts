/**
 * Backend Status Detection Hook
 * 
 * Detects when the backend is down vs other types of errors
 * and provides appropriate UX states for different scenarios.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { apiClient } from '../services/modernAPI';

export type BackendStatus = 
  | 'online'           // Backend is responding normally
  | 'offline'          // Backend is completely down
  | 'degraded'         // Backend is responding but with errors
  | 'checking'         // Currently checking status
  | 'unknown';         // Status unknown

export interface BackendStatusInfo {
  status: BackendStatus;
  lastChecked: Date | null;
  consecutiveFailures: number;
  isRetrying: boolean;
  retryCount: number;
  estimatedDowntime: number; // in minutes
  nextRetryIn: number; // in seconds
}

interface UseBackendStatusOptions {
  checkInterval?: number; // in milliseconds
  maxRetries?: number;
  retryDelay?: number; // in milliseconds
  enableAutoRetry?: boolean;
}

const DEFAULT_OPTIONS: Required<UseBackendStatusOptions> = {
  checkInterval: 30000, // 30 seconds
  maxRetries: 5,
  retryDelay: 5000, // 5 seconds
  enableAutoRetry: true,
};

export function useBackendStatus(options: UseBackendStatusOptions = {}) {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  const [statusInfo, setStatusInfo] = useState<BackendStatusInfo>({
    status: 'checking',
    lastChecked: null,
    consecutiveFailures: 0,
    isRetrying: false,
    retryCount: 0,
    estimatedDowntime: 0,
    nextRetryIn: 0,
  });

  const retryTimeoutRef = useRef<NodeJS.Timeout>();
  const countdownRef = useRef<NodeJS.Timeout>();
  const firstFailureTimeRef = useRef<Date | null>(null);

  // Health check query
  const {
    data: healthData,
    error: healthError,
    isLoading,
    refetch,
    isRefetching,
  } = useQuery({
    queryKey: ['backend-health'],
    queryFn: async () => {
      try {
        const response = await apiClient.healthCheck();
        return response;
      } catch (error) {
        // Classify the error type
        if (error instanceof Error) {
          if (error.message.includes('fetch') || 
              error.message.includes('Network') ||
              error.message.includes('timeout') ||
              error.message.includes('Failed to fetch')) {
            throw new Error('BACKEND_DOWN');
          }
          if (error.message.includes('500') || 
              error.message.includes('502') || 
              error.message.includes('503') || 
              error.message.includes('504')) {
            throw new Error('BACKEND_DEGRADED');
          }
        }
        throw error;
      }
    },
    refetchInterval: opts.checkInterval,
    retry: false, // We handle retries manually
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  });

  // Determine backend status based on query result
  const determineStatus = useCallback((
    isLoading: boolean,
    data: any,
    error: any,
    consecutiveFailures: number
  ): BackendStatus => {
    if (isLoading && consecutiveFailures === 0) return 'checking';
    
    if (data && !error) return 'online';
    
    if (error) {
      const errorMessage = error.message || '';
      
      if (errorMessage.includes('BACKEND_DOWN') || 
          errorMessage.includes('fetch') ||
          errorMessage.includes('Network') ||
          errorMessage.includes('timeout')) {
        return 'offline';
      }
      
      if (errorMessage.includes('BACKEND_DEGRADED') ||
          errorMessage.includes('500') ||
          errorMessage.includes('502') ||
          errorMessage.includes('503') ||
          errorMessage.includes('504')) {
        return 'degraded';
      }
    }
    
    return 'unknown';
  }, []);

  // Update status info when query state changes
  useEffect(() => {
    const newStatus = determineStatus(isLoading, healthData, healthError, statusInfo.consecutiveFailures);
    
    setStatusInfo(prev => {
      const now = new Date();
      let consecutiveFailures = prev.consecutiveFailures;
      let estimatedDowntime = prev.estimatedDowntime;
      
      if (newStatus === 'online') {
        consecutiveFailures = 0;
        estimatedDowntime = 0;
        firstFailureTimeRef.current = null;
      } else if (newStatus === 'offline' || newStatus === 'degraded') {
        if (prev.status === 'online' || prev.status === 'checking') {
          consecutiveFailures = 1;
          firstFailureTimeRef.current = now;
        } else {
          consecutiveFailures = prev.consecutiveFailures + 1;
        }
        
        if (firstFailureTimeRef.current) {
          estimatedDowntime = Math.floor((now.getTime() - firstFailureTimeRef.current.getTime()) / 60000);
        }
      }
      
      return {
        ...prev,
        status: newStatus,
        lastChecked: now,
        consecutiveFailures,
        estimatedDowntime,
        isRetrying: isRefetching,
      };
    });
  }, [isLoading, healthData, healthError, isRefetching, determineStatus]);

  // Manual retry function
  const retry = useCallback(async () => {
    if (statusInfo.isRetrying) return;
    
    setStatusInfo(prev => ({
      ...prev,
      isRetrying: true,
      retryCount: prev.retryCount + 1,
    }));
    
    try {
      await refetch();
    } finally {
      setStatusInfo(prev => ({
        ...prev,
        isRetrying: false,
      }));
    }
  }, [refetch, statusInfo.isRetrying]);

  // Auto retry with exponential backoff
  useEffect(() => {
    if (!opts.enableAutoRetry) return;
    
    if ((statusInfo.status === 'offline' || statusInfo.status === 'degraded') && 
        statusInfo.retryCount < opts.maxRetries && 
        !statusInfo.isRetrying) {
      
      const delay = Math.min(opts.retryDelay * Math.pow(2, statusInfo.retryCount), 60000); // Max 1 minute
      
      // Start countdown
      let countdown = Math.floor(delay / 1000);
      setStatusInfo(prev => ({ ...prev, nextRetryIn: countdown }));
      
      countdownRef.current = setInterval(() => {
        countdown--;
        setStatusInfo(prev => ({ ...prev, nextRetryIn: countdown }));
        
        if (countdown <= 0) {
          clearInterval(countdownRef.current!);
        }
      }, 1000);
      
      retryTimeoutRef.current = setTimeout(() => {
        retry();
      }, delay);
    }
    
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      if (countdownRef.current) {
        clearInterval(countdownRef.current);
      }
    };
  }, [statusInfo.status, statusInfo.retryCount, statusInfo.isRetrying, opts.enableAutoRetry, opts.maxRetries, opts.retryDelay, retry]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      if (countdownRef.current) {
        clearInterval(countdownRef.current);
      }
    };
  }, []);

  // Helper functions
  const isBackendDown = statusInfo.status === 'offline';
  const isBackendDegraded = statusInfo.status === 'degraded';
  const isBackendHealthy = statusInfo.status === 'online';
  const hasConnectivityIssues = isBackendDown || isBackendDegraded;

  return {
    ...statusInfo,
    isBackendDown,
    isBackendDegraded,
    isBackendHealthy,
    hasConnectivityIssues,
    retry,
    // Convenience methods
    getStatusMessage: () => {
      switch (statusInfo.status) {
        case 'online':
          return 'All systems operational';
        case 'offline':
          return 'Backend is currently unavailable';
        case 'degraded':
          return 'Backend is experiencing issues';
        case 'checking':
          return 'Checking backend status...';
        default:
          return 'Backend status unknown';
      }
    },
    getStatusColor: () => {
      switch (statusInfo.status) {
        case 'online':
          return '#28a745'; // green
        case 'offline':
          return '#dc3545'; // red
        case 'degraded':
          return '#ffc107'; // yellow
        case 'checking':
          return '#6c757d'; // gray
        default:
          return '#6c757d'; // gray
      }
    },
  };
}
