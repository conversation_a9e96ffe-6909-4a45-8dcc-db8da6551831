# A/B Test Control Guide 🧪

## Overview

This guide explains how to turn A/B tests on and off in the Cosmetrics AI application. Currently, there's an active A/B test for the landing page redesign that splits traffic 50/50 between the current design (Variant A) and a new editorial design (Variant B).

## 🚀 Quick Start - Pause Test Immediately

### Method 1: Configuration File (Recommended)
**File:** `ui/src/config/abTestConfig.ts`

```typescript
export const AB_TEST_CONFIG = {
  landing_page_redesign: {
    enabled: false, // 🛑 Change this to false to pause test
    // ... rest of config
  }
};
```

### Method 2: Environment Variable
```bash
# Set environment variable
REACT_APP_AB_TEST_ENABLED=false

# Restart application
npm start
```

### Method 3: Admin Panel (Development)
1. Go to `/ab-test-admin` in your browser
2. Click "Pause" button next to the test
3. Test will be paused immediately

## 🎛️ Quick Controls

### Development Mode (Immediate)

If you're in development mode, you can use the floating controls:

1. **Look for the A/B Test Controls** in the bottom-right corner (🧪 icon)
2. **Click to expand** the control panel
3. **Use the buttons** to:
   - Force Variant A (Current design)
   - Force Variant B (Editorial design)
   - View Analytics Dashboard
   - Clear Test Data

### Manual Control (Browser Console)

```javascript
// Force specific variant
localStorage.setItem('ab_test_landing_page_redesign', 'A'); // Current design
localStorage.setItem('ab_test_landing_page_redesign', 'B'); // Editorial design
window.location.reload();

// Clear test assignment (get random assignment again)
localStorage.removeItem('ab_test_landing_page_redesign');
window.location.reload();
```

## 🔧 Permanent Test Control

### 1. Pause the A/B Test (Show Only Current Design)

**File:** `ui/src/App.tsx`

**Change this:**
```typescript
<Route path="" element={<ABTestLanding />} />
```

**To this:**
```typescript
<Route path="" element={<Home />} />
```

**Result:** All users will see the current design (Variant A)

### 2. Pause the A/B Test (Show Only New Design)

**File:** `ui/src/App.tsx`

**Change this:**
```typescript
<Route path="" element={<ABTestLanding />} />
```

**To this:**
```typescript
<Route path="" element={<EditorialLanding />} />
```

**Result:** All users will see the new editorial design (Variant B)

### 3. Completely Disable the A/B Test

**Step 1:** Update the main route in `ui/src/App.tsx`
```typescript
// Replace ABTestLanding with your chosen winner
<Route path="" element={<Home />} /> // or <EditorialLanding />
```

**Step 2:** Remove A/B test components from App.tsx
```typescript
// Remove this line
<ABTestControls />
```

**Step 3:** Clean up imports
```typescript
// Remove these imports if no longer needed
import ABTestLanding from "./screens/Home/ABTestLanding";
import ABTestControls from "./components/ABTestControls";
```

## 📊 Configuration-Based Control

### Create A/B Test Configuration

**File:** `ui/src/config/abTestConfig.ts`

```typescript
export interface ABTestConfig {
  enabled: boolean;
  testName: string;
  variants: string[];
  weights: number[];
  forceVariant?: string; // Force specific variant
}

export const AB_TEST_CONFIG: Record<string, ABTestConfig> = {
  landing_page_redesign: {
    enabled: true, // Set to false to disable
    testName: 'landing_page_redesign',
    variants: ['A', 'B'],
    weights: [0.5, 0.5],
    // forceVariant: 'A' // Uncomment to force variant
  }
};

// Helper function to check if test is enabled
export function isTestEnabled(testName: string): boolean {
  const config = AB_TEST_CONFIG[testName];
  return config?.enabled ?? false;
}

// Helper function to get forced variant
export function getForcedVariant(testName: string): string | undefined {
  const config = AB_TEST_CONFIG[testName];
  return config?.forceVariant;
}
```

### Update ABTestLanding Component

**File:** `ui/src/screens/Home/ABTestLanding.tsx`

```typescript
import { isTestEnabled, getForcedVariant } from '../../config/abTestConfig';

const ABTestLanding: React.FC = () => {
  // Check if test is enabled
  if (!isTestEnabled('landing_page_redesign')) {
    // Test disabled - show default variant
    return <Home />;
  }

  // Check for forced variant
  const forcedVariant = getForcedVariant('landing_page_redesign');
  if (forcedVariant) {
    if (forcedVariant === 'B') {
      return <EditorialLanding />;
    }
    return <Home />;
  }

  // Continue with normal A/B test logic
  const { variant, isLoading, trackEvent } = useABTest({
    testName: 'landing_page_redesign',
    variants: ['A', 'B'],
    weights: [0.5, 0.5]
  });

  // ... rest of component
};
```

## 🎯 Environment-Based Control

### Environment Variables

**File:** `.env` or `.env.local`

```bash
# A/B Test Controls
REACT_APP_AB_TEST_ENABLED=true
REACT_APP_AB_TEST_FORCE_VARIANT=
REACT_APP_AB_TEST_TRAFFIC_SPLIT=50

# Production settings
# REACT_APP_AB_TEST_ENABLED=false
# REACT_APP_AB_TEST_FORCE_VARIANT=A
```

### Environment-Aware Configuration

**File:** `ui/src/config/abTestConfig.ts`

```typescript
export const AB_TEST_CONFIG = {
  landing_page_redesign: {
    enabled: process.env.REACT_APP_AB_TEST_ENABLED === 'true',
    testName: 'landing_page_redesign',
    variants: ['A', 'B'],
    weights: [0.5, 0.5],
    forceVariant: process.env.REACT_APP_AB_TEST_FORCE_VARIANT || undefined
  }
};
```

## 🚀 Deployment Scenarios

### Scenario 1: Pause Test for Maintenance

```typescript
// In abTestConfig.ts
export const AB_TEST_CONFIG = {
  landing_page_redesign: {
    enabled: false, // Temporarily disable
    // ... other config
  }
};
```

### Scenario 2: Force Winner During High Traffic

```typescript
// In abTestConfig.ts
export const AB_TEST_CONFIG = {
  landing_page_redesign: {
    enabled: true,
    forceVariant: 'A', // Force stable variant
    // ... other config
  }
};
```

### Scenario 3: Gradual Rollout

```typescript
// In abTestConfig.ts
export const AB_TEST_CONFIG = {
  landing_page_redesign: {
    enabled: true,
    variants: ['A', 'B'],
    weights: [0.9, 0.1], // 90% current, 10% new
  }
};
```

## 📱 Runtime Controls

### Admin Panel Integration

**File:** `ui/src/components/admin/ABTestAdmin.tsx`

```typescript
const ABTestAdmin: React.FC = () => {
  const [config, setConfig] = useState(AB_TEST_CONFIG);

  const toggleTest = (testName: string) => {
    const newConfig = {
      ...config,
      [testName]: {
        ...config[testName],
        enabled: !config[testName].enabled
      }
    };
    setConfig(newConfig);
    // Save to localStorage or API
    localStorage.setItem('ab_test_admin_config', JSON.stringify(newConfig));
  };

  return (
    <div>
      <h3>A/B Test Controls</h3>
      {Object.entries(config).map(([testName, testConfig]) => (
        <div key={testName}>
          <label>
            <input
              type="checkbox"
              checked={testConfig.enabled}
              onChange={() => toggleTest(testName)}
            />
            {testName} - {testConfig.enabled ? 'Enabled' : 'Disabled'}
          </label>
        </div>
      ))}
    </div>
  );
};
```

## 🔍 Monitoring & Analytics

### Check Test Status

```typescript
// Get current test status
import { getABTestResults } from '../hooks/useABTest';

const results = getABTestResults('landing_page_redesign');
console.log('Test Results:', results);

// Check if test should be stopped
const shouldStop = results.A.conversions > 100 && results.B.conversions > 100;
if (shouldStop) {
  console.log('Test has enough data for decision');
}
```

### Analytics Dashboard

Visit `/ab-test-dashboard` to see:
- Real-time test performance
- Conversion rates by variant
- Statistical significance
- Recommendation to stop test

## ⚠️ Important Considerations

### Before Disabling Tests

1. **Check Analytics** - Ensure you have enough data
2. **Document Results** - Save test results for future reference
3. **Gradual Rollout** - Consider gradual winner implementation
4. **Monitor Performance** - Watch for any issues after changes

### Production Checklist

- [ ] Remove development indicators
- [ ] Update analytics tracking
- [ ] Test both variants work correctly
- [ ] Set up monitoring for chosen variant
- [ ] Document decision rationale

## 🎉 Quick Actions

### Immediately Pause Test (Emergency)

```bash
# Set environment variable
export REACT_APP_AB_TEST_ENABLED=false

# Or update config file
# Set enabled: false in abTestConfig.ts

# Redeploy application
```

### Resume Test

```bash
# Set environment variable
export REACT_APP_AB_TEST_ENABLED=true

# Or update config file
# Set enabled: true in abTestConfig.ts

# Redeploy application
```

This guide provides multiple ways to control A/B tests, from immediate development controls to production-ready configuration management. Choose the method that best fits your deployment workflow and requirements.
