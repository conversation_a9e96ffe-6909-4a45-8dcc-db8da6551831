# Offline & Backend Down Handling

## Problem Solved

Previously, when the backend was down, the frontend displayed a blank screen with the generic message "Error loading user/session data." This provided a poor user experience with no indication of what was happening or when it might be resolved.

## Solution Overview

We've implemented a comprehensive offline/backend down handling system that provides:

1. **🍳 Engaging Loading States** - Fun messages like "We're cooking up something amazing..."
2. **📡 Clear Offline Indicators** - Humorous but informative messages when backend is down
3. **🔄 Automatic Retry Logic** - Smart retry mechanisms with exponential backoff
4. **🎯 Context-Aware UX** - Different handling for different types of errors
5. **🧪 Easy Testing** - Tools to test offline scenarios

## Key Components

### 1. Backend Status Detection (`useBackendStatus`)

Automatically detects backend status and provides real-time updates:

```typescript
const backendStatus = useBackendStatus({
  checkInterval: 30000,     // Check every 30 seconds
  enableAutoRetry: true,    // Auto-retry on failures
  maxRetries: 5,           // Max retry attempts
});

// Status can be: 'online', 'offline', 'degraded', 'checking', 'unknown'
```

### 2. Engaging UI Components

**Cooking Loader** - For loading states:
```typescript
<CookingLoader message="We're cooking up something amazing..." />
```

**Offline Message** - For backend down scenarios:
```typescript
<OfflineMessage onRetry={handleRetry} />
```

**Degraded Performance** - For slow backend responses:
```typescript
<DegradedPerformance onRetry={handleRetry} />
```

### 3. Higher-Order Components

**Backend Status Wrapper** - Automatically handles backend status:
```typescript
<BackendStatusWrapper 
  showLoadingOnCheck={true}
  showDegradedWarning={true}
  fallbackOnDegraded={true}
>
  <YourComponent />
</BackendStatusWrapper>
```

**HOC Pattern**:
```typescript
const EnhancedComponent = withBackendStatus(YourComponent, {
  showLoadingOnCheck: true,
  fallbackOnDegraded: true,
});
```

### 4. Global Context

**Backend Status Provider** - Provides global status throughout the app:
```typescript
// In App.tsx
<BackendStatusProvider>
  <YourApp />
</BackendStatusProvider>

// In any component
const { status, retry, isBackendDown } = useBackendStatusContext();
```

## Funny Messages

### Cooking/Loading Messages
- 🍳 "We're cooking up something amazing..."
- 👨‍🍳 "Our chefs are preparing your data..."
- 🔥 "Heating up the servers..."
- 🥘 "Stirring the database soup..."

### Offline Messages
- 🛠️ "Our hamsters are taking a coffee break"
- 🔌 "Someone tripped over the internet cable"
- 🚀 "We're upgrading to warp speed"
- 🧘‍♂️ "The backend is meditating for better performance"

### Degraded Performance Messages
- 🐌 "Moving at the speed of a caffeinated snail"
- 🎢 "Taking the scenic route through the data"
- 🚂 "All aboard the slow train to Dataville"

## Implementation Examples

### Dashboard Components

Before:
```typescript
if (userError || sessionsError) {
  return <p>Error loading user/session data.</p>;
}
```

After:
```typescript
if (userError || sessionsError) {
  const hasBackendError = isBackendDownError(userError) || isBackendDownError(sessionsError);
  
  if (hasBackendError) {
    return (
      <BackendStatusWrapper 
        showLoadingOnCheck={false}
        showDegradedWarning={true}
        fallbackOnDegraded={false}
      >
        <div>Content shown when backend is healthy</div>
      </BackendStatusWrapper>
    );
  }
  
  return (
    <div className="text-center my-4 py-5">
      <p className="text-danger mb-3">
        We're having trouble loading your data right now.
      </p>
      <p className="text-muted">
        This might be a temporary issue. Please try refreshing the page.
      </p>
      <button onClick={() => window.location.reload()}>
        Refresh Page
      </button>
    </div>
  );
}
```

### React Query Integration

Enhanced retry logic for backend down scenarios:
```typescript
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error) => {
        const isBackendDown = /* detection logic */;
        return isBackendDown ? failureCount < 5 : failureCount < 3;
      },
      retryDelay: (attemptIndex, error) => {
        const isBackendDown = /* detection logic */;
        return isBackendDown 
          ? Math.min(2000 * Math.pow(2, attemptIndex), 30000)  // 2s, 4s, 8s, 16s, 30s
          : Math.min(1000 * Math.pow(2, attemptIndex), 30000); // 1s, 2s, 4s, 8s, 16s, 30s
      },
    },
  },
});
```

## Error Detection

The system automatically detects backend down scenarios:

```typescript
function isBackendDownError(error: any): boolean {
  const errorMessage = error.message || '';
  const errorStatus = error.status || error.response?.status;
  
  // Network/fetch errors
  if (errorMessage.includes('fetch') ||
      errorMessage.includes('Network request failed') ||
      errorMessage.includes('timeout')) {
    return true;
  }
  
  // Server errors indicating backend is down
  if (errorStatus === 502 || errorStatus === 503 || errorStatus === 504) {
    return true;
  }
  
  return false;
}
```

## Testing

### Manual Testing
1. Stop your backend server
2. Disconnect from the internet
3. Use browser dev tools to simulate network failures
4. Block the API domain in your hosts file

### Demo Component
Use the `BackendStatusDemo` component to test all scenarios:
```typescript
import BackendStatusDemo from './components/BackendStatusDemo';

// Add to your routes for testing
<Route path="/backend-status-demo" element={<BackendStatusDemo />} />
```

## Benefits

### User Experience
- ✅ **No more blank screens** - Always show something meaningful
- ✅ **Clear communication** - Users know what's happening
- ✅ **Engaging content** - Humorous messages reduce frustration
- ✅ **Actionable feedback** - Clear retry options

### Developer Experience
- ✅ **Easy to implement** - Simple HOCs and hooks
- ✅ **Consistent handling** - Same patterns throughout the app
- ✅ **Automatic detection** - No manual error classification needed
- ✅ **Testing tools** - Built-in demo and testing components

### Performance
- ✅ **Smart retries** - Exponential backoff prevents server overload
- ✅ **Automatic recovery** - Seamless transition back to normal operation
- ✅ **Efficient polling** - Reasonable check intervals
- ✅ **Context-aware** - Different strategies for different error types

## Migration Guide

### Existing Components

1. **Replace generic error messages**:
   ```typescript
   // Before
   if (error) return <p>Error loading data.</p>;
   
   // After
   if (error) {
     if (isBackendDownError(error)) {
       return <BackendStatusWrapper>...</BackendStatusWrapper>;
     }
     return <ImprovedErrorMessage />;
   }
   ```

2. **Add backend status context**:
   ```typescript
   // In your component
   const { canPerformOperation } = useBackendAvailability();
   
   if (!canPerformOperation('write')) {
     return <div>Cannot save changes while offline</div>;
   }
   ```

3. **Wrap critical components**:
   ```typescript
   export default withBackendStatus(YourComponent, {
     showLoadingOnCheck: true,
     fallbackOnDegraded: true,
   });
   ```

## Configuration

### Global Settings
```typescript
<BackendStatusProvider
  checkInterval={30000}      // 30 seconds
  enableAutoRetry={true}     // Enable automatic retries
  maxRetries={5}             // Maximum retry attempts
>
  <App />
</BackendStatusProvider>
```

### Component-Level Settings
```typescript
<BackendStatusWrapper
  showLoadingOnCheck={true}     // Show cooking loader when checking
  showDegradedWarning={true}    // Show warning for degraded performance
  fallbackOnDegraded={true}     // Still show content when degraded
  enableAutoRetry={true}        // Enable automatic retries
>
  <YourComponent />
</BackendStatusWrapper>
```

This system transforms the user experience from frustrating blank screens to engaging, informative, and actionable feedback when the backend is unavailable.
