/**
 * User Validation Utilities
 * 
 * Helps validate user data and prevent invalid API calls
 * that could result in 404 errors.
 */

export interface UserValidationResult {
  isValid: boolean;
  userId?: number;
  error?: string;
  source: 'localStorage' | 'currentUser' | 'none';
}

/**
 * Get the current user ID from various sources with validation
 */
export function getCurrentUserId(): UserValidationResult {
  // Try to get user ID from localStorage first (legacy auth)
  try {
    const auth = localStorage.getItem("auth");
    if (auth) {
      const parsed = JSON.parse(auth);
      if (parsed?.user?.id && typeof parsed.user.id === 'number' && parsed.user.id > 0) {
        return {
          isValid: true,
          userId: parsed.user.id,
          source: 'localStorage'
        };
      }
    }
  } catch (error) {
    console.warn('Failed to parse localStorage auth:', error);
  }

  // If no valid user ID found
  return {
    isValid: false,
    error: 'No valid user ID found in localStorage',
    source: 'none'
  };
}

/**
 * Validate a user ID before making API calls
 */
export function validateUserId(userId: any): { isValid: boolean; error?: string } {
  if (userId === null || userId === undefined) {
    return { isValid: false, error: 'User ID is null or undefined' };
  }

  if (typeof userId !== 'number') {
    return { isValid: false, error: `User ID must be a number, got ${typeof userId}` };
  }

  if (userId <= 0) {
    return { isValid: false, error: `User ID must be positive, got ${userId}` };
  }

  if (!Number.isInteger(userId)) {
    return { isValid: false, error: `User ID must be an integer, got ${userId}` };
  }

  return { isValid: true };
}

/**
 * Safe wrapper for API calls that require a user ID
 */
export function withValidUserId<T>(
  userId: any,
  callback: (validUserId: number) => T,
  fallback?: T
): T | undefined {
  const validation = validateUserId(userId);
  
  if (!validation.isValid) {
    console.warn(`Invalid user ID for API call: ${validation.error}`);
    return fallback;
  }

  return callback(userId);
}

/**
 * Get user info for debugging
 */
export function getUserDebugInfo() {
  const userValidation = getCurrentUserId();
  
  return {
    validation: userValidation,
    localStorage: {
      auth: localStorage.getItem("auth"),
      accessToken: localStorage.getItem("access_token"),
      refreshToken: localStorage.getItem("refresh_token"),
    },
    parsed: (() => {
      try {
        const auth = localStorage.getItem("auth");
        return auth ? JSON.parse(auth) : null;
      } catch {
        return null;
      }
    })(),
  };
}

/**
 * Check if user is properly authenticated
 */
export function isUserAuthenticated(): boolean {
  const userValidation = getCurrentUserId();
  const hasTokens = localStorage.getItem("access_token") || localStorage.getItem("auth");
  
  return userValidation.isValid && !!hasTokens;
}

/**
 * Log user authentication state for debugging
 */
export function logUserAuthState(context: string = 'Unknown') {
  const debugInfo = getUserDebugInfo();
  
  console.group(`🔍 User Auth State - ${context}`);
  console.log('User Validation:', debugInfo.validation);
  console.log('Has Access Token:', !!debugInfo.localStorage.accessToken);
  console.log('Has Refresh Token:', !!debugInfo.localStorage.refreshToken);
  console.log('Has Legacy Auth:', !!debugInfo.localStorage.auth);
  console.log('Parsed Auth:', debugInfo.parsed);
  console.log('Is Authenticated:', isUserAuthenticated());
  console.groupEnd();
}

/**
 * Create a safe user ID for API calls with fallback
 */
export function getSafeUserId(fallbackUserId?: number): number | null {
  const userValidation = getCurrentUserId();
  
  if (userValidation.isValid && userValidation.userId) {
    return userValidation.userId;
  }
  
  if (fallbackUserId && validateUserId(fallbackUserId).isValid) {
    console.warn(`Using fallback user ID: ${fallbackUserId}`);
    return fallbackUserId;
  }
  
  console.error('No valid user ID available for API call');
  return null;
}
