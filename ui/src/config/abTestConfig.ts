/**
 * A/B Test Configuration
 * 
 * Central configuration for all A/B tests in the application.
 * Modify these settings to control test behavior without code changes.
 */

export interface ABTestConfig {
  enabled: boolean;
  testName: string;
  variants: string[];
  weights: number[];
  forceVariant?: string; // Force specific variant for all users
  description?: string;
  startDate?: string;
  endDate?: string;
}

/**
 * A/B Test Configurations
 * 
 * To pause a test: Set enabled to false
 * To force a variant: Set forceVariant to 'A' or 'B'
 * To adjust traffic: Modify weights array
 */
export const AB_TEST_CONFIG: Record<string, ABTestConfig> = {
  landing_page_redesign: {
    enabled: false, // 🎛️ MAIN CONTROL: Set to false to pause test
    testName: 'landing_page_redesign',
    variants: ['A', 'B'],
    weights: [0.5, 0.5], // 50/50 split
    // forceVariant: 'A', // 🎯 FORCE VARIANT: Uncomment to force specific variant
    description: 'Landing page redesign test - Current vs Editorial design',
    startDate: '2024-01-01',
    // endDate: '2024-02-01', // Uncomment to auto-stop test
  },

  // Add more tests here as needed
  // example_test: {
  //   enabled: false,
  //   testName: 'example_test',
  //   variants: ['A', 'B'],
  //   weights: [0.5, 0.5],
  //   description: 'Example test description',
  // }
};

/**
 * Environment-based overrides
 * These take precedence over the config above
 */
const ENV_OVERRIDES = {
  enabled: process.env.REACT_APP_AB_TEST_ENABLED === 'true',
  forceVariant: process.env.REACT_APP_AB_TEST_FORCE_VARIANT,
  trafficSplit: process.env.REACT_APP_AB_TEST_TRAFFIC_SPLIT,
};

/**
 * Helper function to check if a test is enabled
 */
export function isTestEnabled(testName: string): boolean {
  const config = getTestConfig(testName);
  if (!config) return false;

  // Check if test has ended
  if (config.endDate && new Date() > new Date(config.endDate)) {
    console.log(`A/B Test '${testName}' has ended on ${config.endDate}`);
    return false;
  }

  return config.enabled;
}

/**
 * Helper function to get forced variant
 */
export function getForcedVariant(testName: string): string | undefined {
  const config = getTestConfig(testName);
  return config?.forceVariant;
}

/**
 * Helper function to get test configuration with environment overrides
 */
export function getTestConfig(testName: string): ABTestConfig | undefined {
  const baseConfig = AB_TEST_CONFIG[testName];
  if (!baseConfig) return undefined;

  // Apply environment overrides for landing page test
  if (testName === 'landing_page_redesign') {
    const config = { ...baseConfig };

    // Override enabled state from environment
    if (process.env.REACT_APP_AB_TEST_ENABLED !== undefined) {
      config.enabled = ENV_OVERRIDES.enabled;
    }

    // Override forced variant from environment
    if (ENV_OVERRIDES.forceVariant) {
      config.forceVariant = ENV_OVERRIDES.forceVariant;
    }

    // Override traffic split from environment
    if (ENV_OVERRIDES.trafficSplit) {
      const split = parseFloat(ENV_OVERRIDES.trafficSplit) / 100;
      config.weights = [1 - split, split];
    }

    return config;
  }

  return baseConfig;
}

/**
 * Helper function to get all active tests
 */
export function getActiveTests(): Record<string, ABTestConfig> {
  const activeTests: Record<string, ABTestConfig> = {};

  Object.keys(AB_TEST_CONFIG).forEach(testName => {
    if (isTestEnabled(testName)) {
      const config = getTestConfig(testName);
      if (config) {
        activeTests[testName] = config;
      }
    }
  });

  return activeTests;
}

/**
 * Helper function to get test status summary
 */
export function getTestStatusSummary() {
  const summary = {
    total: Object.keys(AB_TEST_CONFIG).length,
    active: 0,
    paused: 0,
    forced: 0,
    ended: 0,
  };

  Object.keys(AB_TEST_CONFIG).forEach(testName => {
    const config = getTestConfig(testName);
    if (!config) return;

    if (config.endDate && new Date() > new Date(config.endDate)) {
      summary.ended++;
    } else if (!config.enabled) {
      summary.paused++;
    } else if (config.forceVariant) {
      summary.forced++;
    } else {
      summary.active++;
    }
  });

  return summary;
}

/**
 * Development helper to log test status
 */
export function logTestStatus() {
  if (process.env.NODE_ENV !== 'development') return;

  console.group('🧪 A/B Test Status');

  Object.entries(AB_TEST_CONFIG).forEach(([testName, config]) => {
    const currentConfig = getTestConfig(testName);
    const isActive = isTestEnabled(testName);
    const forcedVariant = getForcedVariant(testName);

    console.log(`📊 ${testName}:`, {
      enabled: isActive,
      forced: forcedVariant || 'None',
      weights: currentConfig?.weights,
      description: config.description,
    });
  });

  const summary = getTestStatusSummary();
  console.log('📈 Summary:', summary);

  console.groupEnd();
}

// Log test status in development
if (process.env.NODE_ENV === 'development') {
  logTestStatus();
}

/**
 * Quick control functions for development/admin use
 */
export const ABTestControls = {
  /**
   * Pause all tests
   */
  pauseAll: () => {
    Object.keys(AB_TEST_CONFIG).forEach(testName => {
      AB_TEST_CONFIG[testName].enabled = false;
    });
    console.log('🛑 All A/B tests paused');
  },

  /**
   * Resume all tests
   */
  resumeAll: () => {
    Object.keys(AB_TEST_CONFIG).forEach(testName => {
      AB_TEST_CONFIG[testName].enabled = true;
    });
    console.log('▶️ All A/B tests resumed');
  },

  /**
   * Force variant for specific test
   */
  forceVariant: (testName: string, variant: string) => {
    if (AB_TEST_CONFIG[testName]) {
      AB_TEST_CONFIG[testName].forceVariant = variant;
      console.log(`🎯 Forced ${testName} to variant ${variant}`);
    }
  },

  /**
   * Clear forced variant
   */
  clearForced: (testName: string) => {
    if (AB_TEST_CONFIG[testName]) {
      delete AB_TEST_CONFIG[testName].forceVariant;
      console.log(`🔄 Cleared forced variant for ${testName}`);
    }
  },

  /**
   * Get current status
   */
  status: () => {
    logTestStatus();
    return getTestStatusSummary();
  }
};

// Make controls available in development console
if (process.env.NODE_ENV === 'development') {
  (window as any).ABTestControls = ABTestControls;
  console.log('🎛️ A/B Test controls available at window.ABTestControls');
}
