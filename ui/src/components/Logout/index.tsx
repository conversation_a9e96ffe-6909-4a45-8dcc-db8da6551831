import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";

import axios from "axios";
import { reduxLogout } from "../../redux/authSlice";
import { queryClient, apiClient } from "../../services/modernAPI";
import { updateUser } from "../../redux/UserSlice";

const LogoutButton = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleLogout = async () => {
    try {
      // JWT-based authentication - no need for logout API call
      // await axios.post("/api/users/logout/", {});

      // Clear all authentication data
      localStorage.removeItem("auth");

      // Clear modern API client tokens and cache
      apiClient.clearAllTokens();
      queryClient.clear();

      // Dispatch Redux actions
      dispatch(reduxLogout());
      dispatch(updateUser({ user: {} }));

      // Redirect to login page
      navigate("/login");
    } catch (error) {
      console.error("Logout failed", error);
      // Clear everything anyway on error
      localStorage.removeItem("auth");
      apiClient.clearAllTokens();
      queryClient.clear();
      dispatch(reduxLogout());
      dispatch(updateUser({ user: {} }));
      navigate("/login");
    }
  };

  return <button onClick={handleLogout}>Logout</button>;
};

export default LogoutButton;
