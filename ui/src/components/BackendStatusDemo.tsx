/**
 * Backend Status Demo Component
 * 
 * A demo component to test and showcase the backend status handling
 * functionality. Useful for development and testing.
 */

import React, { useState } from 'react';
import { <PERSON>, But<PERSON>, Alert, Badge, Row, Col } from 'react-bootstrap';
import { 
  BackendStatusDisplay, 
  CookingLoader, 
  OfflineMessage, 
  DegradedPerformance 
} from './ui/BackendStatusComponents';
import { BackendStatusWrapper, useBackendStatusWrapper } from './hoc/withBackendStatus';
import { useBackendStatusContext, BackendStatusIndicator } from '../contexts/BackendStatusContext';

const BackendStatusDemo: React.FC = () => {
  const [simulatedStatus, setSimulatedStatus] = useState<'online' | 'offline' | 'degraded' | 'checking'>('online');
  const backendStatus = useBackendStatusContext();

  const { renderWithStatus } = useBackendStatusWrapper({
    showLoadingOnCheck: true,
    showDegradedWarning: true,
    fallbackOnDegraded: true,
  });

  return (
    <div className="p-4">
      <h2 className="mb-4">🧪 Backend Status Demo</h2>
      
      {/* Current Status */}
      <Card className="mb-4">
        <Card.Header>
          <strong>Current Backend Status</strong>
        </Card.Header>
        <Card.Body>
          <div className="d-flex align-items-center justify-content-between mb-3">
            <div>
              <Badge 
                bg={backendStatus.status === 'online' ? 'success' : 
                    backendStatus.status === 'offline' ? 'danger' : 
                    backendStatus.status === 'degraded' ? 'warning' : 'secondary'}
                className="me-2"
              >
                {backendStatus.status.toUpperCase()}
              </Badge>
              <span>{backendStatus.getStatusMessage()}</span>
            </div>
            <Button 
              size="sm" 
              variant="outline-primary" 
              onClick={backendStatus.retry}
              disabled={backendStatus.isRetrying}
            >
              {backendStatus.isRetrying ? 'Checking...' : 'Check Status'}
            </Button>
          </div>
          
          <BackendStatusIndicator showWhenHealthy compact={false} />
          
          <div className="mt-3">
            <small className="text-muted">
              <strong>Details:</strong><br />
              Last Checked: {backendStatus.lastChecked?.toLocaleTimeString() || 'Never'}<br />
              Consecutive Failures: {backendStatus.consecutiveFailures}<br />
              Estimated Downtime: {backendStatus.estimatedDowntime} minutes<br />
              Retry Count: {backendStatus.retryCount}
            </small>
          </div>
        </Card.Body>
      </Card>

      {/* Component Demos */}
      <Row>
        <Col md={6}>
          <Card className="mb-4">
            <Card.Header>
              <strong>🍳 Cooking Loader</strong>
            </Card.Header>
            <Card.Body>
              <CookingLoader message="We're whipping up something delicious..." />
            </Card.Body>
          </Card>
        </Col>
        
        <Col md={6}>
          <Card className="mb-4">
            <Card.Header>
              <strong>📡 Offline Message</strong>
            </Card.Header>
            <Card.Body>
              <OfflineMessage onRetry={() => console.log('Retry clicked')} />
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row>
        <Col md={6}>
          <Card className="mb-4">
            <Card.Header>
              <strong>🐌 Degraded Performance</strong>
            </Card.Header>
            <Card.Body>
              <DegradedPerformance onRetry={() => console.log('Retry clicked')} />
            </Card.Body>
          </Card>
        </Col>
        
        <Col md={6}>
          <Card className="mb-4">
            <Card.Header>
              <strong>📊 Full Status Display</strong>
            </Card.Header>
            <Card.Body>
              <BackendStatusDisplay 
                showRetry 
                showDetails 
                compact={false}
              />
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Backend Status Wrapper Demo */}
      <Card className="mb-4">
        <Card.Header>
          <strong>🎭 Backend Status Wrapper Demo</strong>
        </Card.Header>
        <Card.Body>
          <p className="mb-3">
            This demonstrates how content is automatically wrapped with backend status handling:
          </p>
          
          <BackendStatusWrapper
            showLoadingOnCheck={true}
            showDegradedWarning={true}
            fallbackOnDegraded={true}
          >
            <Alert variant="success">
              🎉 This content is only shown when the backend is healthy!
              <br />
              <small>Current time: {new Date().toLocaleTimeString()}</small>
            </Alert>
          </BackendStatusWrapper>
        </Card.Body>
      </Card>

      {/* Hook Demo */}
      <Card className="mb-4">
        <Card.Header>
          <strong>🪝 Hook-based Wrapper Demo</strong>
        </Card.Header>
        <Card.Body>
          <p className="mb-3">
            This demonstrates the hook-based approach for more control:
          </p>
          
          {renderWithStatus(
            <Alert variant="info">
              🚀 This content is rendered using the hook-based wrapper!
              <br />
              <small>Backend Status: {backendStatus.status}</small>
            </Alert>
          )}
        </Card.Body>
      </Card>

      {/* Testing Controls */}
      <Card>
        <Card.Header>
          <strong>🎮 Testing Controls</strong>
        </Card.Header>
        <Card.Body>
          <p className="mb-3">
            Use these buttons to test different scenarios (Note: These don't actually change the backend status, 
            they're just for UI demonstration):
          </p>
          
          <div className="d-flex gap-2 flex-wrap">
            <Button 
              variant={simulatedStatus === 'online' ? 'success' : 'outline-success'}
              size="sm"
              onClick={() => setSimulatedStatus('online')}
            >
              ✅ Simulate Online
            </Button>
            
            <Button 
              variant={simulatedStatus === 'checking' ? 'info' : 'outline-info'}
              size="sm"
              onClick={() => setSimulatedStatus('checking')}
            >
              🍳 Simulate Checking
            </Button>
            
            <Button 
              variant={simulatedStatus === 'degraded' ? 'warning' : 'outline-warning'}
              size="sm"
              onClick={() => setSimulatedStatus('degraded')}
            >
              🐌 Simulate Degraded
            </Button>
            
            <Button 
              variant={simulatedStatus === 'offline' ? 'danger' : 'outline-danger'}
              size="sm"
              onClick={() => setSimulatedStatus('offline')}
            >
              📡 Simulate Offline
            </Button>
          </div>
          
          <Alert variant="info" className="mt-3 mb-0">
            <small>
              <strong>💡 Pro Tip:</strong> To test real backend down scenarios, you can:
              <ul className="mb-0 mt-2">
                <li>Stop your backend server</li>
                <li>Disconnect from the internet</li>
                <li>Use browser dev tools to simulate network failures</li>
                <li>Block the API domain in your hosts file</li>
              </ul>
            </small>
          </Alert>
        </Card.Body>
      </Card>
    </div>
  );
};

export default BackendStatusDemo;
