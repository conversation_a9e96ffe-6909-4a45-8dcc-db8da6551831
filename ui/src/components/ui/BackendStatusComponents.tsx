/**
 * Backend Status Components
 * 
 * Engaging and humorous components for handling backend down scenarios
 * with better UX than generic error messages.
 */

import React, { useState, useEffect } from 'react';
import { Card, Button, Alert, Badge, ProgressBar } from 'react-bootstrap';
import {
  Wifi,
  WifiOff,
  RefreshCw,
  Coffee,
  Wrench,
  Clock,
  Zap,
  Heart,
  Smile,
  ChefHat
} from 'lucide-react';
import { useBackendStatus, BackendStatus } from '../../hooks/useBackendStatus';

// ============================================================================
// Funny Messages
// ============================================================================

const COOKING_MESSAGES = [
  "🍳 We're cooking up something amazing...",
  "👨‍🍳 Our chefs are preparing your data...",
  "🔥 Heating up the servers...",
  "🥘 Stirring the database soup...",
  "🍰 Baking fresh features...",
  "🧑‍🍳 Seasoning the algorithms...",
  "🍕 Preparing your digital pizza...",
  "🥗 Mixing up a fresh salad of data...",
];

const OFFLINE_MESSAGES = [
  "🛠️ Our hamsters are taking a coffee break",
  "🔌 Someone tripped over the internet cable",
  "🚀 We're upgrading to warp speed",
  "🎭 The servers are having a dramatic moment",
  "🧘‍♂️ The backend is meditating for better performance",
  "🎪 The data circus is setting up",
  "🏗️ Construction zone: Building better experiences",
  "🎨 Our artists are painting new features",
];

const DEGRADED_MESSAGES = [
  "🐌 Moving at the speed of a caffeinated snail",
  "🎢 Taking the scenic route through the data",
  "🚂 All aboard the slow train to Dataville",
  "🐢 Channeling our inner turtle energy",
  "🎯 Precision over speed (we're being extra careful)",
  "🧩 Solving the puzzle one piece at a time",
  "🎪 Juggling data like a pro (but carefully)",
  "🎭 Performing a delicate data ballet",
];

// ============================================================================
// Main Backend Status Component
// ============================================================================

interface BackendStatusDisplayProps {
  showRetry?: boolean;
  showDetails?: boolean;
  compact?: boolean;
  className?: string;
}

export const BackendStatusDisplay: React.FC<BackendStatusDisplayProps> = ({
  showRetry = true,
  showDetails = false,
  compact = false,
  className = ''
}) => {
  const backendStatus = useBackendStatus();
  const [currentMessage, setCurrentMessage] = useState('');
  const [messageIndex, setMessageIndex] = useState(0);

  // Rotate messages every 3 seconds
  useEffect(() => {
    let messages: string[] = [];

    switch (backendStatus.status) {
      case 'checking':
        messages = COOKING_MESSAGES;
        break;
      case 'offline':
        messages = OFFLINE_MESSAGES;
        break;
      case 'degraded':
        messages = DEGRADED_MESSAGES;
        break;
      default:
        return;
    }

    setCurrentMessage(messages[messageIndex % messages.length]);

    const interval = setInterval(() => {
      setMessageIndex(prev => prev + 1);
    }, 3000);

    return () => clearInterval(interval);
  }, [backendStatus.status, messageIndex]);

  if (backendStatus.status === 'online') {
    return null; // Don't show anything when backend is healthy
  }

  if (compact) {
    return (
      <Alert variant={getVariant(backendStatus.status)} className={`d-flex align-items-center ${className}`}>
        {getIcon(backendStatus.status)}
        <span className="ms-2">{currentMessage}</span>
        {showRetry && (
          <Button
            size="sm"
            variant="outline-primary"
            onClick={backendStatus.retry}
            disabled={backendStatus.isRetrying}
            className="ms-auto"
          >
            <RefreshCw size={14} className={backendStatus.isRetrying ? 'animate-spin' : ''} />
          </Button>
        )}
      </Alert>
    );
  }

  return (
    <Card className={`text-center ${className}`} style={{ borderRadius: '16px' }}>
      <Card.Body className="py-5">
        <div className="mb-4">
          {getIcon(backendStatus.status, 64)}
        </div>

        <h3 className="mb-3" style={{ color: backendStatus.getStatusColor() }}>
          {getTitle(backendStatus.status)}
        </h3>

        <p className="text-muted mb-4" style={{ fontSize: '1.1rem' }}>
          {currentMessage}
        </p>

        {backendStatus.status === 'offline' && backendStatus.estimatedDowntime > 0 && (
          <div className="mb-4">
            <Badge bg="secondary" className="px-3 py-2">
              <Clock size={16} className="me-2" />
              Down for {backendStatus.estimatedDowntime} minute{backendStatus.estimatedDowntime !== 1 ? 's' : ''}
            </Badge>
          </div>
        )}

        {backendStatus.nextRetryIn > 0 && (
          <div className="mb-4">
            <p className="text-muted mb-2">
              Auto-retry in {backendStatus.nextRetryIn} second{backendStatus.nextRetryIn !== 1 ? 's' : ''}
            </p>
            <ProgressBar
              now={(5 - backendStatus.nextRetryIn) * 20}
              variant="info"
              style={{ height: '4px' }}
            />
          </div>
        )}

        {showRetry && (
          <div className="d-flex gap-3 justify-content-center flex-wrap">
            <Button
              variant="primary"
              onClick={backendStatus.retry}
              disabled={backendStatus.isRetrying}
              className="d-flex align-items-center gap-2"
            >
              <RefreshCw size={16} className={backendStatus.isRetrying ? 'animate-spin' : ''} />
              {backendStatus.isRetrying ? 'Checking...' : 'Try Again'}
            </Button>
          </div>
        )}

        {showDetails && (
          <details className="mt-4 text-start">
            <summary className="cursor-pointer text-muted">
              <small>Technical Details</small>
            </summary>
            <div className="mt-2 p-3 bg-light rounded">
              <small>
                <strong>Status:</strong> {backendStatus.status}<br />
                <strong>Last Checked:</strong> {backendStatus.lastChecked?.toLocaleTimeString()}<br />
                <strong>Consecutive Failures:</strong> {backendStatus.consecutiveFailures}<br />
                <strong>Retry Count:</strong> {backendStatus.retryCount}
              </small>
            </div>
          </details>
        )}
      </Card.Body>
    </Card>
  );
};

// ============================================================================
// Specialized Components
// ============================================================================

export const CookingLoader: React.FC<{ message?: string; }> = ({
  message = "We're cooking up something amazing..."
}) => {
  return (
    <div className="text-center py-5">
      <div className="mb-4">
        <ChefHat size={64} color="#667eea" className="animate-bounce" />
      </div>
      <h4 className="mb-3" style={{ color: '#667eea' }}>
        Kitchen's Open!
      </h4>
      <p className="text-muted" style={{ fontSize: '1.1rem' }}>
        {message}
      </p>
      <div className="mt-4">
        <div className="d-inline-flex align-items-center gap-2 px-3 py-2 bg-light rounded-pill">
          <Coffee size={16} color="#8b5cf6" />
          <span className="small text-muted">Brewing fresh data...</span>
        </div>
      </div>
    </div>
  );
};

export const OfflineMessage: React.FC<{ onRetry?: () => void; }> = ({ onRetry }) => {
  const [messageIndex, setMessageIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setMessageIndex(prev => (prev + 1) % OFFLINE_MESSAGES.length);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="text-center py-5">
      <div className="mb-4">
        <WifiOff size={64} color="#e74c3c" />
      </div>
      <h4 className="mb-3" style={{ color: '#e74c3c' }}>
        Oops! We're Temporarily Offline
      </h4>
      <p className="text-muted mb-4" style={{ fontSize: '1.1rem' }}>
        {OFFLINE_MESSAGES[messageIndex]}
      </p>
      {onRetry && (
        <Button variant="primary" onClick={onRetry} className="d-flex align-items-center gap-2 mx-auto">
          <RefreshCw size={16} />
          Check Again
        </Button>
      )}
    </div>
  );
};

export const DegradedPerformance: React.FC<{ onRetry?: () => void; }> = ({ onRetry }) => {
  return (
    <Alert variant="warning" className="d-flex align-items-center">
      <Zap size={20} className="me-3" />
      <div className="flex-grow-1">
        <strong>Slow Lane Mode</strong>
        <div className="small">
          We're experiencing some hiccups, but we're still here for you! 🐌
        </div>
      </div>
      {onRetry && (
        <Button size="sm" variant="outline-warning" onClick={onRetry}>
          <RefreshCw size={14} />
        </Button>
      )}
    </Alert>
  );
};

// ============================================================================
// Helper Functions
// ============================================================================

function getIcon(status: BackendStatus, size: number = 24) {
  switch (status) {
    case 'checking':
      return <ChefHat size={size} color="#667eea" className="animate-bounce" />;
    case 'offline':
      return <WifiOff size={size} color="#e74c3c" />;
    case 'degraded':
      return <Zap size={size} color="#f39c12" />;
    case 'online':
      return <Wifi size={size} color="#28a745" />;
    default:
      return <Wrench size={size} color="#6c757d" />;
  }
}

function getTitle(status: BackendStatus): string {
  switch (status) {
    case 'checking':
      return "Kitchen's Open!";
    case 'offline':
      return "We're Temporarily Offline";
    case 'degraded':
      return "Running in Slow Lane Mode";
    case 'online':
      return "All Systems Go!";
    default:
      return "Status Unknown";
  }
}

function getVariant(status: BackendStatus): string {
  switch (status) {
    case 'checking':
      return 'info';
    case 'offline':
      return 'danger';
    case 'degraded':
      return 'warning';
    case 'online':
      return 'success';
    default:
      return 'secondary';
  }
}
