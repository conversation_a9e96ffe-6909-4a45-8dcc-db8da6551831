import React, { useEffect, useState, useMemo, useCallback } from "react";
import { AxiosError } from "axios";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { ArrowClockwise } from "react-bootstrap-icons";

import api from "../../api";
// import SessionDataCard from "../SessionDataCard";
import { SessionApiResponse } from "../../constants";
import { useApiRetry } from "../../hooks/useRetry";
import { handleApiError } from "../../services/errorHandler";
// import SessionRawData from "../SessionRawData";
import SessionRawData from "../SessionRawData";
import { getHairIdValue } from "../SessionRawData";

interface SessionDetails {
  sessionGuid: string;
  questionIds: number[];
  hairFeature: string;
}

const SessionReplies: React.FC<SessionDetails> = ({ sessionGuid, questionIds, hairFeature }) => {
  const [data, setData] = useState<SessionApiResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  const url = useMemo(() => {
    const params = new URLSearchParams();
    questionIds.forEach((id) => params.append("question_ids", id.toString()));
    return `/quiz/replies/sessions/${sessionGuid}?${params.toString()}`;
  }, [sessionGuid, questionIds]);

  const fetchData = useCallback(async () => {
    const response = await api.get<SessionApiResponse>(url);
    setData(response.data);
    setError(null);
    return response.data;
  }, [url]);

  const { execute: retryFetch, isRetrying } = useApiRetry(fetchData, {
    maxAttempts: 3,
    onError: (error, attempt) => {
      const axiosError = error as AxiosError<{ message?: string; }>;
      const status = axiosError.response?.status;
      const msg = axiosError.response?.data?.message || axiosError.message || "Unknown error";
      console.log(`Error fetching data (attempt ${attempt})`);

      const errorMap: Record<number, string> = {
        400: `Bad request: ${msg}`,
        404: `Not found: ${msg}`,
        500: `Server error: ${msg}`,
      };

      const errorMessage = errorMap[status || 0] || `Error: ${msg}`;
      setError(errorMessage);
    },
    onMaxAttemptsReached: (lastError) => {
      handleApiError("Failed to load session data", lastError, {
        component: 'SessionReplies',
        metadata: { sessionGuid, questionIds }
      });
    }
  });

  useEffect(() => {
    if (!sessionGuid) return;

    // Add a small delay to prevent rapid successive calls
    const timeoutId = setTimeout(() => {
      retryFetch();
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [sessionGuid, url]);

  if (isRetrying) {
    return (
      <div className="d-flex justify-content-center my-5">
        <Spinner animation="border" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="danger" className="my-4 d-flex justify-content-between align-items-center">
        <span>{error}</span>
        <Button variant="outline-light" size="sm" onClick={retryFetch} aria-label="Retry fetch">
          <ArrowClockwise />
        </Button>
      </Alert>
    );
  }

  if (!data || data.data.length === 0) {
    return (
      <Alert variant="warning" className="my-4">
        No replies found for this session.
      </Alert>
    );
  }


  return <SessionRawData payload={data} hairFeature={hairFeature} />;
  // return <SessionDataCard payload={data} hairFeature={hairFeature} />;

};

export { getHairIdValue };
export default SessionReplies;
