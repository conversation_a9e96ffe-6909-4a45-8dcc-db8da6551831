/**
 * A/B Test Admin Panel
 * 
 * Simple admin interface for controlling A/B tests without code changes.
 * Access via /ab-test-admin route (development only).
 */

import React, { useState, useEffect } from 'react';
import { Card, Button, Form, Alert, Badge, Table } from 'react-bootstrap';
import { 
  AB_TEST_CONFIG, 
  getTestStatusSummary, 
  getActiveTests,
  ABTestControls,
  isTestEnabled,
  getForcedVariant 
} from '../../config/abTestConfig';
import { getABTestResults } from '../../hooks/useABTest';
import { Play, Pause, Target, RotateCcw, BarChart3 } from 'lucide-react';

const ABTestAdmin: React.FC = () => {
  const [config, setConfig] = useState(AB_TEST_CONFIG);
  const [results, setResults] = useState<Record<string, any>>({});
  const [lastUpdated, setLastUpdated] = useState(new Date());

  // Load results for all tests
  useEffect(() => {
    const loadResults = () => {
      const allResults: Record<string, any> = {};
      Object.keys(config).forEach(testName => {
        allResults[testName] = getABTestResults(testName);
      });
      setResults(allResults);
      setLastUpdated(new Date());
    };

    loadResults();
    const interval = setInterval(loadResults, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, [config]);

  const toggleTest = (testName: string) => {
    const newConfig = {
      ...config,
      [testName]: {
        ...config[testName],
        enabled: !config[testName].enabled
      }
    };
    setConfig(newConfig);
    
    // Update the actual config (in a real app, this would be an API call)
    Object.assign(AB_TEST_CONFIG[testName], newConfig[testName]);
    
    // Reload page to apply changes
    setTimeout(() => window.location.reload(), 500);
  };

  const forceVariant = (testName: string, variant: string | null) => {
    const newConfig = {
      ...config,
      [testName]: {
        ...config[testName],
        forceVariant: variant || undefined
      }
    };
    setConfig(newConfig);
    
    // Update the actual config
    if (variant) {
      AB_TEST_CONFIG[testName].forceVariant = variant;
    } else {
      delete AB_TEST_CONFIG[testName].forceVariant;
    }
    
    // Reload page to apply changes
    setTimeout(() => window.location.reload(), 500);
  };

  const clearTestData = (testName: string) => {
    if (window.confirm(`Clear all data for test "${testName}"? This cannot be undone.`)) {
      localStorage.removeItem(`ab_test_${testName}`);
      localStorage.removeItem(`ab_test_events_${testName}`);
      window.location.reload();
    }
  };

  const summary = getTestStatusSummary();

  return (
    <div className="container mt-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2>🧪 A/B Test Admin Panel</h2>
        <Badge bg="info">
          Last Updated: {lastUpdated.toLocaleTimeString()}
        </Badge>
      </div>

      {/* Summary Cards */}
      <div className="row mb-4">
        <div className="col-md-3">
          <Card className="text-center">
            <Card.Body>
              <h5 className="text-success">{summary.active}</h5>
              <small>Active Tests</small>
            </Card.Body>
          </Card>
        </div>
        <div className="col-md-3">
          <Card className="text-center">
            <Card.Body>
              <h5 className="text-warning">{summary.paused}</h5>
              <small>Paused Tests</small>
            </Card.Body>
          </Card>
        </div>
        <div className="col-md-3">
          <Card className="text-center">
            <Card.Body>
              <h5 className="text-info">{summary.forced}</h5>
              <small>Forced Tests</small>
            </Card.Body>
          </Card>
        </div>
        <div className="col-md-3">
          <Card className="text-center">
            <Card.Body>
              <h5 className="text-secondary">{summary.ended}</h5>
              <small>Ended Tests</small>
            </Card.Body>
          </Card>
        </div>
      </div>

      {/* Global Controls */}
      <Card className="mb-4">
        <Card.Header>
          <strong>Global Controls</strong>
        </Card.Header>
        <Card.Body>
          <div className="d-flex gap-2">
            <Button 
              variant="outline-danger" 
              size="sm"
              onClick={() => {
                ABTestControls.pauseAll();
                setTimeout(() => window.location.reload(), 500);
              }}
            >
              <Pause size={16} className="me-1" />
              Pause All Tests
            </Button>
            <Button 
              variant="outline-success" 
              size="sm"
              onClick={() => {
                ABTestControls.resumeAll();
                setTimeout(() => window.location.reload(), 500);
              }}
            >
              <Play size={16} className="me-1" />
              Resume All Tests
            </Button>
            <Button 
              variant="outline-info" 
              size="sm"
              onClick={() => window.open('/ab-test-dashboard', '_blank')}
            >
              <BarChart3 size={16} className="me-1" />
              View Analytics
            </Button>
          </div>
        </Card.Body>
      </Card>

      {/* Individual Test Controls */}
      {Object.entries(config).map(([testName, testConfig]) => {
        const testResults = results[testName] || {};
        const isActive = isTestEnabled(testName);
        const forced = getForcedVariant(testName);
        
        return (
          <Card key={testName} className="mb-3">
            <Card.Header className="d-flex justify-content-between align-items-center">
              <div>
                <strong>{testName}</strong>
                <div className="d-flex gap-2 mt-1">
                  <Badge bg={isActive ? 'success' : 'secondary'}>
                    {isActive ? 'Active' : 'Paused'}
                  </Badge>
                  {forced && (
                    <Badge bg="warning">
                      Forced: {forced}
                    </Badge>
                  )}
                </div>
              </div>
              <div className="d-flex gap-2">
                <Button
                  size="sm"
                  variant={isActive ? 'outline-danger' : 'outline-success'}
                  onClick={() => toggleTest(testName)}
                >
                  {isActive ? <Pause size={14} /> : <Play size={14} />}
                  {isActive ? 'Pause' : 'Resume'}
                </Button>
              </div>
            </Card.Header>
            
            <Card.Body>
              {testConfig.description && (
                <p className="text-muted mb-3">{testConfig.description}</p>
              )}
              
              {/* Variant Controls */}
              <div className="mb-3">
                <h6>Force Variant:</h6>
                <div className="d-flex gap-2">
                  {testConfig.variants.map(variant => (
                    <Button
                      key={variant}
                      size="sm"
                      variant={forced === variant ? 'primary' : 'outline-primary'}
                      onClick={() => forceVariant(testName, variant)}
                    >
                      <Target size={14} className="me-1" />
                      Force {variant}
                    </Button>
                  ))}
                  <Button
                    size="sm"
                    variant="outline-secondary"
                    onClick={() => forceVariant(testName, null)}
                    disabled={!forced}
                  >
                    <RotateCcw size={14} className="me-1" />
                    Clear Force
                  </Button>
                </div>
              </div>

              {/* Test Results */}
              {Object.keys(testResults).length > 0 && (
                <div className="mb-3">
                  <h6>Current Results:</h6>
                  <Table size="sm" className="mb-0">
                    <thead>
                      <tr>
                        <th>Variant</th>
                        <th>Views</th>
                        <th>Clicks</th>
                        <th>Conversions</th>
                        <th>CTR</th>
                        <th>CVR</th>
                      </tr>
                    </thead>
                    <tbody>
                      {Object.values(testResults).map((stats: any) => (
                        <tr key={stats.variant}>
                          <td><Badge bg="secondary">{stats.variant}</Badge></td>
                          <td>{stats.views || 0}</td>
                          <td>{stats.clicks || 0}</td>
                          <td>{stats.conversions || 0}</td>
                          <td>{stats.clickRate || '0.00'}%</td>
                          <td>{stats.conversionRate || '0.00'}%</td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </div>
              )}

              {/* Configuration Details */}
              <details>
                <summary className="text-muted" style={{ cursor: 'pointer' }}>
                  <small>Configuration Details</small>
                </summary>
                <div className="mt-2 p-2 bg-light rounded">
                  <small>
                    <strong>Variants:</strong> {testConfig.variants.join(', ')}<br />
                    <strong>Weights:</strong> {testConfig.weights.map(w => `${(w * 100).toFixed(0)}%`).join(' / ')}<br />
                    <strong>Start Date:</strong> {testConfig.startDate || 'Not set'}<br />
                    <strong>End Date:</strong> {testConfig.endDate || 'Not set'}
                  </small>
                </div>
              </details>

              {/* Danger Zone */}
              <div className="mt-3 pt-3 border-top">
                <Button
                  size="sm"
                  variant="outline-danger"
                  onClick={() => clearTestData(testName)}
                >
                  Clear Test Data
                </Button>
                <small className="text-muted ms-2">
                  This will clear all stored test data and assignments
                </small>
              </div>
            </Card.Body>
          </Card>
        );
      })}

      {/* Instructions */}
      <Alert variant="info">
        <h6>💡 Quick Instructions:</h6>
        <ul className="mb-0">
          <li><strong>Pause Test:</strong> Click the pause button to stop the test and show default variant</li>
          <li><strong>Force Variant:</strong> Force all users to see a specific variant</li>
          <li><strong>Clear Force:</strong> Return to normal A/B testing behavior</li>
          <li><strong>View Analytics:</strong> See detailed performance metrics</li>
          <li><strong>Clear Data:</strong> Reset all test data (use with caution)</li>
        </ul>
      </Alert>
    </div>
  );
};

export default ABTestAdmin;
