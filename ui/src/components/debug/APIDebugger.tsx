/**
 * API Debugger Component
 * 
 * Helps debug API issues by showing current authentication state,
 * user information, and API call status.
 */

import React, { useState } from 'react';
import { Card, <PERSON><PERSON>, Alert, Badge, Table } from 'react-bootstrap';
import { useQuestionnaireStatus } from '../../hooks/api/useQuestionnaireStatus';
import { useUserProfile, useCurrentUser } from '../../hooks/api/useModernAPI';
import { useBackendStatusContext } from '../../contexts/BackendStatusContext';
import { apiClient } from '../../services/modernAPI';
import { Eye, EyeOff, RefreshCw, User, Database, Wifi } from 'lucide-react';

const APIDebugger: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [testUserId, setTestUserId] = useState<number>(12);
  
  // Get current authentication state
  const auth = JSON.parse(localStorage.getItem("auth") || "{}");
  const localUser = auth?.user;
  
  // API hooks
  const { data: currentUser, error: currentUserError, isLoading: currentUserLoading } = useCurrentUser();
  const { data: userProfile, error: profileError, isLoading: profileLoading } = useUserProfile(testUserId);
  const { data: questionnaireStatus, error: questionnaireError, isLoading: questionnaireLoading } = useQuestionnaireStatus(testUserId);
  
  // Backend status
  const backendStatus = useBackendStatusContext();

  if (!isVisible) {
    return (
      <div style={{ position: 'fixed', bottom: '20px', right: '20px', zIndex: 9999 }}>
        <Button
          variant="outline-primary"
          size="sm"
          onClick={() => setIsVisible(true)}
          style={{
            borderRadius: '50%',
            width: '50px',
            height: '50px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Eye size={20} />
        </Button>
      </div>
    );
  }

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      width: '400px',
      maxHeight: '80vh',
      overflowY: 'auto',
      zIndex: 9999,
      backgroundColor: 'white',
      borderRadius: '12px',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
      border: '1px solid #e2e8f0',
    }}>
      <Card>
        <Card.Header className="d-flex justify-content-between align-items-center">
          <strong>🐛 API Debugger</strong>
          <Button
            variant="outline-secondary"
            size="sm"
            onClick={() => setIsVisible(false)}
          >
            <EyeOff size={16} />
          </Button>
        </Card.Header>
        
        <Card.Body style={{ fontSize: '0.875rem' }}>
          {/* Backend Status */}
          <div className="mb-3">
            <h6 className="d-flex align-items-center gap-2">
              <Wifi size={16} />
              Backend Status
            </h6>
            <Badge 
              bg={backendStatus.status === 'online' ? 'success' : 
                  backendStatus.status === 'offline' ? 'danger' : 'warning'}
            >
              {backendStatus.status.toUpperCase()}
            </Badge>
            <div className="small text-muted mt-1">
              {backendStatus.getStatusMessage()}
            </div>
          </div>

          {/* Authentication State */}
          <div className="mb-3">
            <h6 className="d-flex align-items-center gap-2">
              <User size={16} />
              Authentication
            </h6>
            <Table size="sm" className="mb-0">
              <tbody>
                <tr>
                  <td><strong>Is Authenticated:</strong></td>
                  <td>
                    <Badge bg={apiClient.isAuthenticated() ? 'success' : 'danger'}>
                      {apiClient.isAuthenticated() ? 'Yes' : 'No'}
                    </Badge>
                  </td>
                </tr>
                <tr>
                  <td><strong>Local User ID:</strong></td>
                  <td>{localUser?.id || 'None'}</td>
                </tr>
                <tr>
                  <td><strong>Local User Email:</strong></td>
                  <td>{localUser?.email || 'None'}</td>
                </tr>
                <tr>
                  <td><strong>Access Token:</strong></td>
                  <td>
                    <Badge bg={localStorage.getItem('access_token') ? 'success' : 'danger'}>
                      {localStorage.getItem('access_token') ? 'Present' : 'Missing'}
                    </Badge>
                  </td>
                </tr>
              </tbody>
            </Table>
          </div>

          {/* Current User API */}
          <div className="mb-3">
            <h6 className="d-flex align-items-center gap-2">
              <Database size={16} />
              Current User API
            </h6>
            <div className="d-flex align-items-center gap-2 mb-2">
              <Badge bg={currentUserLoading ? 'info' : currentUserError ? 'danger' : 'success'}>
                {currentUserLoading ? 'Loading' : currentUserError ? 'Error' : 'Success'}
              </Badge>
              {currentUserError && (
                <small className="text-danger">
                  {currentUserError.message}
                </small>
              )}
            </div>
            {currentUser && (
              <div className="small">
                <strong>ID:</strong> {currentUser.id}<br />
                <strong>Email:</strong> {currentUser.email}<br />
                <strong>Name:</strong> {currentUser.first_name} {currentUser.last_name}
              </div>
            )}
          </div>

          {/* Test User Profile */}
          <div className="mb-3">
            <h6>User Profile Test</h6>
            <div className="d-flex gap-2 mb-2">
              <input
                type="number"
                value={testUserId}
                onChange={(e) => setTestUserId(Number(e.target.value))}
                className="form-control form-control-sm"
                style={{ width: '80px' }}
              />
              <Button
                size="sm"
                variant="outline-primary"
                onClick={() => window.location.reload()}
              >
                <RefreshCw size={14} />
              </Button>
            </div>
            
            <div className="d-flex align-items-center gap-2 mb-2">
              <Badge bg={profileLoading ? 'info' : profileError ? 'danger' : 'success'}>
                Profile: {profileLoading ? 'Loading' : profileError ? 'Error' : 'Success'}
              </Badge>
            </div>
            
            {profileError && (
              <Alert variant="danger" className="py-2">
                <small>
                  <strong>Profile Error:</strong><br />
                  {profileError.message}
                </small>
              </Alert>
            )}
            
            {userProfile && (
              <div className="small">
                <strong>Profile ID:</strong> {userProfile.id}<br />
                <strong>User ID:</strong> {userProfile.user_id}<br />
                <strong>Completed Questions:</strong> {userProfile.completed_questions}<br />
                <strong>Questionnaire Complete:</strong> {userProfile.questionaire_completed ? 'Yes' : 'No'}
              </div>
            )}
          </div>

          {/* Questionnaire Status */}
          <div className="mb-3">
            <h6>Questionnaire Status</h6>
            <div className="d-flex align-items-center gap-2 mb-2">
              <Badge bg={questionnaireLoading ? 'info' : questionnaireError ? 'danger' : 'success'}>
                Status: {questionnaireLoading ? 'Loading' : questionnaireError ? 'Error' : 'Success'}
              </Badge>
            </div>
            
            {questionnaireError && (
              <Alert variant="danger" className="py-2">
                <small>
                  <strong>Questionnaire Error:</strong><br />
                  {questionnaireError.message}
                </small>
              </Alert>
            )}
            
            {questionnaireStatus && (
              <div className="small">
                <strong>Status:</strong> {questionnaireStatus.status}<br />
                <strong>Completed:</strong> {questionnaireStatus.completed_questions}/{questionnaireStatus.total_questions}<br />
                <strong>Finished:</strong> {questionnaireStatus.questionaire_completed ? 'Yes' : 'No'}
              </div>
            )}
          </div>

          {/* API Endpoints */}
          <div className="mb-3">
            <h6>API Endpoints Being Called</h6>
            <div className="small">
              <div><strong>Current User:</strong> /api/users/me</div>
              <div><strong>User Profile:</strong> /api/users/{testUserId}/profile</div>
              <div><strong>Questions Count:</strong> /api/questionaire/questions/count</div>
            </div>
          </div>

          {/* Recommendations */}
          <Alert variant="info" className="py-2">
            <small>
              <strong>💡 Troubleshooting Tips:</strong><br />
              • Check if user ID {testUserId} exists in the database<br />
              • Verify backend is running on correct port<br />
              • Check browser network tab for detailed error info<br />
              • Ensure user has proper permissions
            </small>
          </Alert>
        </Card.Body>
      </Card>
    </div>
  );
};

export default APIDebugger;
