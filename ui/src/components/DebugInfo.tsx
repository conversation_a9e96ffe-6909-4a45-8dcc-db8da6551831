/**
 * Debug Info Component
 * 
 * Shows debug information about user state, localStorage, and API calls
 * for troubleshooting authentication and user session issues.
 */

import React from 'react';
import { <PERSON>, Al<PERSON>, Badge, Button } from 'react-bootstrap';
import { useLocalUser } from '../hooks/api/useLocalUser';
import { useUserSessionGuid } from '../hooks/api/useUserSessionGuid';
import { useAuthStatus } from '../hooks/api/useModernAPI';
import { apiClient, queryClient } from '../services/modernAPI';
import { runAllAuthTests } from '../tests/authFlow.test';

const DebugInfo: React.FC = () => {
  // Get user data from different sources
  const { data: localUser, isLoading: localUserLoading, error: localUserError } = useLocalUser();
  const { isAuthenticated, user: authUser, isLoading: authLoading } = useAuthStatus();
  const { data: sessionData, isLoading: sessionLoading, error: sessionError } = useUserSessionGuid(localUser?.id);

  // Get localStorage data
  const getLocalStorageData = () => {
    try {
      const auth = localStorage.getItem('auth');
      const parsed = auth ? JSON.parse(auth) : null;
      return { raw: auth, parsed };
    } catch (error) {
      return { raw: localStorage.getItem('auth'), parsed: null, error: error instanceof Error ? error.message : String(error) };
    }
  };

  const localStorageData = getLocalStorageData();

  // Debug actions
  const handleClearCache = () => {
    queryClient.clear();
    console.log('React Query cache cleared');
  };

  const handleClearTokens = () => {
    apiClient.clearAllTokens();
    console.log('All tokens cleared');
  };

  const handleReloadTokens = () => {
    apiClient.reloadTokensFromStorage();
    console.log('Tokens reloaded from storage');
  };

  const handleRunTests = () => {
    console.log('Running authentication tests...');
    runAllAuthTests();
  };

  return (
    <div className="debug-info p-3" style={{ fontSize: '0.875rem' }}>
      <h5>🐛 Debug Information</h5>

      {/* Debug Controls */}
      <Card className="mb-3">
        <Card.Header>
          <strong>Debug Controls</strong>
        </Card.Header>
        <Card.Body>
          <div className="d-flex gap-2 flex-wrap">
            <Button size="sm" variant="outline-warning" onClick={handleClearCache}>
              Clear Cache
            </Button>
            <Button size="sm" variant="outline-danger" onClick={handleClearTokens}>
              Clear Tokens
            </Button>
            <Button size="sm" variant="outline-info" onClick={handleReloadTokens}>
              Reload Tokens
            </Button>
            <Button size="sm" variant="outline-success" onClick={handleRunTests}>
              Run Auth Tests
            </Button>
          </div>
          <div className="mt-2">
            <small className="text-muted">
              Token Status: {JSON.stringify(apiClient.getTokenStatus(), null, 2)}
            </small>
          </div>
        </Card.Body>
      </Card>

      {/* Authentication Status */}
      <Card className="mb-3">
        <Card.Header>
          <strong>Authentication Status</strong>
        </Card.Header>
        <Card.Body>
          <div className="mb-2">
            <Badge bg={isAuthenticated ? 'success' : 'danger'}>
              {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
            </Badge>
            {authLoading && <Badge bg="warning" className="ms-2">Loading...</Badge>}
          </div>

          {authUser && (
            <div>
              <strong>Auth User:</strong>
              <pre className="mt-1 p-2 bg-light rounded">
                {JSON.stringify(authUser, null, 2)}
              </pre>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Local User Data */}
      <Card className="mb-3">
        <Card.Header>
          <strong>Local User (from localStorage)</strong>
        </Card.Header>
        <Card.Body>
          {localUserLoading && <Badge bg="warning">Loading...</Badge>}
          {localUserError && (
            <Alert variant="danger" className="mb-2">
              Error: {localUserError.message}
            </Alert>
          )}

          {localUser ? (
            <div>
              <div className="mb-2">
                <Badge bg="success">User Found</Badge>
                <span className="ms-2">ID: {localUser.id}</span>
              </div>
              <pre className="p-2 bg-light rounded">
                {JSON.stringify(localUser, null, 2)}
              </pre>
            </div>
          ) : (
            <Alert variant="warning">No local user found</Alert>
          )}
        </Card.Body>
      </Card>

      {/* Session Data */}
      <Card className="mb-3">
        <Card.Header>
          <strong>User Sessions</strong>
        </Card.Header>
        <Card.Body>
          {sessionLoading && <Badge bg="warning">Loading...</Badge>}
          {sessionError && (
            <Alert variant="danger" className="mb-2">
              Error: {sessionError.message}
            </Alert>
          )}

          {sessionData ? (
            <div>
              <div className="mb-2">
                <Badge bg="success">{sessionData.length} Sessions Found</Badge>
              </div>
              <pre className="p-2 bg-light rounded">
                {JSON.stringify(sessionData, null, 2)}
              </pre>
            </div>
          ) : (
            <Alert variant="info">No sessions found</Alert>
          )}
        </Card.Body>
      </Card>

      {/* localStorage Data */}
      <Card className="mb-3">
        <Card.Header>
          <strong>localStorage Data</strong>
        </Card.Header>
        <Card.Body>
          <div className="mb-2">
            <strong>Raw auth data:</strong>
          </div>
          <pre className="p-2 bg-light rounded mb-3">
            {localStorageData.raw || 'null'}
          </pre>

          {localStorageData.parsed && (
            <div>
              <div className="mb-2">
                <strong>Parsed auth data:</strong>
              </div>
              <pre className="p-2 bg-light rounded">
                {JSON.stringify(localStorageData.parsed, null, 2)}
              </pre>
            </div>
          )}

          {localStorageData.error && (
            <Alert variant="danger">
              Parse Error: {localStorageData.error}
            </Alert>
          )}
        </Card.Body>
      </Card>

      {/* API Endpoints */}
      <Card className="mb-3">
        <Card.Header>
          <strong>API Information</strong>
        </Card.Header>
        <Card.Body>
          <div className="mb-2">
            <strong>Base URL:</strong> {process.env.REACT_APP_API_URL || 'http://localhost:8000'}
          </div>
          <div className="mb-2">
            <strong>User Session Endpoint:</strong>
            {localUser?.id ? (
              <code>/quiz/replies/sessions/user/{localUser.id}</code>
            ) : (
              <span className="text-muted">No user ID available</span>
            )}
          </div>
        </Card.Body>
      </Card>

      {/* Recommendations */}
      <Card>
        <Card.Header>
          <strong>🔧 Troubleshooting</strong>
        </Card.Header>
        <Card.Body>
          <div className="mb-2">
            <strong>Current Issues:</strong>
          </div>
          <ul className="mb-0">
            {!isAuthenticated && (
              <li>User is not authenticated - consider logging in</li>
            )}
            {!localUser && (
              <li>No user data in localStorage - authentication may have expired</li>
            )}
            {sessionError && (
              <li>Session API error - check if user exists in database</li>
            )}
            {localUser && !sessionData?.length && (
              <li>User has no quiz sessions - complete a quiz to generate sessions</li>
            )}
          </ul>
        </Card.Body>
      </Card>
    </div>
  );
};

export default DebugInfo;
