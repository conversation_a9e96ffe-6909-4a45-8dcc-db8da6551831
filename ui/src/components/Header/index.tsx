import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useUserActions } from "../../hooks/user.actions";
import CosmetricsLogo from "../../assets/images/cosmetrics-logo.svg";
import APIDebugger from "../debug/APIDebugger";
import {
  User,
  Menu,
  X,
  ChevronDown,
  LogOut,
  Settings,
  BarChart3,
  Home,
  HelpCircle,
  Info
} from "lucide-react";

// Modern Navigation Styles
const navStyles = {
  nav: {
    position: 'fixed' as const,
    top: 0,
    left: 0,
    right: 0,
    height: '72px',
    background: 'rgba(255, 255, 255, 0.98)',
    backdropFilter: 'blur(20px)',
    borderBottom: '1px solid #E2E8F0',
    zIndex: 1000,
    transition: 'all 0.3s ease',
  },
  container: {
    maxWidth: '1200px',
    margin: '0 auto',
    padding: '0 24px',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  logo: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
    textDecoration: 'none',
    color: '#1F2937',
    fontWeight: 700,
    fontSize: '20px',
  },
  logoImage: {
    height: '40px',
    width: 'auto',
  },
  navLinks: {
    display: 'flex',
    alignItems: 'center',
    gap: '32px',
    listStyle: 'none',
    margin: 0,
    padding: 0,
  },
  navLink: {
    textDecoration: 'none',
    color: '#1F2937',
    fontWeight: 600,
    fontSize: '15px',
    padding: '10px 18px',
    borderRadius: '12px',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  },
  authButtons: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
  },
  button: {
    padding: '10px 20px',
    borderRadius: '8px',
    border: 'none',
    fontWeight: 500,
    fontSize: '14px',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    textDecoration: 'none',
  },
  primaryButton: {
    background: 'linear-gradient(135deg, #4300FF 0%)',
    color: 'white',
    boxShadow: '0 4px 16px rgba(67, 0, 255, 0.3)',
    fontWeight: 600,
  },
  secondaryButton: {
    background: 'transparent',
    color: '#4300FF',
    border: '2px solid #4300FF',
    fontWeight: 600,
  },
  userMenu: {
    position: 'relative' as const,
  },
  userButton: {
    background: 'white',
    border: '2px solid #E2E8F0',
    borderRadius: '12px',
    padding: '10px 18px',
    display: 'flex',
    alignItems: 'center',
    gap: '10px',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
    color: '#1F2937',
    fontWeight: 600,
  },
  dropdown: {
    position: 'absolute' as const,
    top: '100%',
    right: 0,
    marginTop: '8px',
    background: 'white',
    borderRadius: '12px',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
    border: '1px solid #e2e8f0',
    minWidth: '200px',
    overflow: 'hidden',
    zIndex: 1001,
  },
  dropdownItem: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
    padding: '14px 18px',
    color: '#1F2937',
    textDecoration: 'none',
    fontSize: '14px',
    fontWeight: 500,
    transition: 'all 0.2s ease',
    cursor: 'pointer',
    border: 'none',
    background: 'none',
    width: '100%',
    textAlign: 'left' as const,
  },
  mobileMenuButton: {
    display: 'none',
    background: 'none',
    border: '2px solid #E2E8F0',
    cursor: 'pointer',
    padding: '10px',
    borderRadius: '12px',
    color: '#1F2937',
    transition: 'all 0.2s ease',
  },
  mobileMenu: {
    position: 'fixed' as const,
    top: '72px',
    left: 0,
    right: 0,
    background: 'rgba(255, 255, 255, 0.98)',
    backdropFilter: 'blur(20px)',
    borderBottom: '1px solid #E2E8F0',
    padding: '24px',
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '20px',
    zIndex: 999,
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  },
};

const Header: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { logout } = useUserActions(navigate);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  // Get user from localStorage
  const auth = JSON.parse(localStorage.getItem("auth") || "{}");
  const user = auth?.user;

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth >= 768) {
        setIsMobileMenuOpen(false);
      }
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setIsUserMenuOpen(false);
      setIsMobileMenuOpen(false);
    };
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  const handleLogout = () => {
    logout();
    navigate("/login");
    setIsUserMenuOpen(false);
  };

  const handleNavClick = (path: string) => {
    navigate(path);
    setIsMobileMenuOpen(false);
  };

  const navItems = [
    { label: 'Home', path: '/', icon: Home },
    { label: 'How it works', path: '/#how-it-works', icon: HelpCircle },
    { label: 'Hair ID', path: '/#hair-id', icon: BarChart3 },
    { label: 'About us', path: '/#about-us', icon: Info },
  ];

  return (
    <>
      <nav style={{
        ...navStyles.nav,
        background: isScrolled ? 'rgba(255, 255, 255, 0.98)' : 'rgba(255, 255, 255, 0.95)',
        boxShadow: isScrolled ? '0 4px 20px rgba(0, 0, 0, 0.08)' : 'none',
      }}>
        <div style={navStyles.container}>
          {/* Logo */}
          <a
            href="/"
            style={navStyles.logo}
            onClick={(e) => {
              e.preventDefault();
              handleNavClick('/');
            }}
          >
            <img src={CosmetricsLogo} alt="Cosmetrics AI" style={navStyles.logoImage} />
          </a>

          {/* Desktop Navigation */}
          {!isMobile && (
            <ul style={navStyles.navLinks}>
              {navItems.map((item) => {
                const Icon = item.icon;
                const isActive = location.pathname === item.path ||
                  (item.path.includes('#') && location.hash === item.path.split('#')[1]);

                return (
                  <li key={item.path}>
                    <a
                      href={item.path}
                      style={{
                        ...navStyles.navLink,
                        color: isActive ? '#4300FF' : '#1F2937',
                        background: isActive ? 'linear-gradient(135deg, rgba(67, 0, 255, 0.1) 0%, rgba(255, 67, 0, 0.1) 100%)' : 'transparent',
                        fontWeight: isActive ? 700 : 600,
                      }}
                      onClick={(e) => {
                        e.preventDefault();
                        if (item.path.includes('#')) {
                          const element = document.querySelector(item.path.split('#')[1]);
                          element?.scrollIntoView({ behavior: 'smooth' });
                        } else {
                          handleNavClick(item.path);
                        }
                      }}
                      onMouseEnter={(e) => {
                        if (!isActive) {
                          e.currentTarget.style.background = 'linear-gradient(135deg, rgba(67, 0, 255, 0.08) 0%, rgba(255, 67, 0, 0.08) 100%)';
                          e.currentTarget.style.color = '#4300FF';
                          e.currentTarget.style.transform = 'translateY(-1px)';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (!isActive) {
                          e.currentTarget.style.background = 'transparent';
                          e.currentTarget.style.color = '#1F2937';
                          e.currentTarget.style.transform = 'translateY(0)';
                        }
                      }}
                    >
                      <Icon size={16} />
                      {item.label}
                    </a>
                  </li>
                );
              })}
            </ul>
          )}

          {/* Auth Section */}
          <div style={navStyles.authButtons}>
            {!isMobile && user && (
              <a
                href="/quiz"
                style={{
                  ...navStyles.button,
                  ...navStyles.primaryButton,
                }}
                onClick={(e) => {
                  e.preventDefault();
                  handleNavClick('/quiz');
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(67, 0, 255, 0.4)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 4px 16px rgba(67, 0, 255, 0.3)';
                }}
              >
                Start Quiz
              </a>
            )}

            {user ? (
              <div style={navStyles.userMenu}>
                <button
                  style={{
                    ...navStyles.userButton,
                    borderColor: isUserMenuOpen ? '#667eea' : '#e2e8f0',
                    boxShadow: isUserMenuOpen ? '0 4px 12px rgba(102, 126, 234, 0.15)' : '0 2px 8px rgba(0, 0, 0, 0.08)',
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsUserMenuOpen(!isUserMenuOpen);
                  }}
                >
                  <div style={{
                    width: '32px',
                    height: '32px',
                    borderRadius: '50%',
                    background: '#ff4300',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '14px',
                    fontWeight: 600,
                  }}>
                    {(user.first_name?.[0] || user.email?.[0] || 'U').toUpperCase()}
                  </div>
                  <span style={{ color: '#1F2937', fontWeight: 600 }}>
                    {user.first_name || user.email?.split('@')[0] || 'User'}
                  </span>
                  <ChevronDown
                    size={16}
                    style={{
                      color: '#9ca3af',
                      transform: isUserMenuOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                      transition: 'transform 0.2s ease',
                    }}
                  />
                </button>

                {isUserMenuOpen && (
                  <div style={navStyles.dropdown}>
                    <a
                      href="/dashboard"
                      style={navStyles.dropdownItem}
                      onClick={(e) => {
                        e.preventDefault();
                        handleNavClick('/dashboard');
                        setIsUserMenuOpen(false);
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.background = 'linear-gradient(135deg, rgba(67, 0, 255, 0.05) 0%, rgba(255, 67, 0, 0.05) 100%)';
                        e.currentTarget.style.color = '#4300FF';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.background = 'transparent';
                        e.currentTarget.style.color = '#1F2937';
                      }}
                    >
                      <BarChart3 size={16} />
                      Dashboard
                    </a>
                    <a
                      href="/dashboard/account"
                      style={navStyles.dropdownItem}
                      onClick={(e) => {
                        e.preventDefault();
                        handleNavClick('/dashboard/account');
                        setIsUserMenuOpen(false);
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.background = 'linear-gradient(135deg, rgba(67, 0, 255, 0.05) 0%, rgba(255, 67, 0, 0.05) 100%)';
                        e.currentTarget.style.color = '#4300FF';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.background = 'transparent';
                        e.currentTarget.style.color = '#1F2937';
                      }}
                    >
                      <Settings size={16} />
                      Profile
                    </a>
                    <button
                      style={{
                        ...navStyles.dropdownItem,
                        borderTop: '1px solid #f1f5f9',
                        color: '#EF4444',
                        fontWeight: 600,
                      }}
                      onClick={handleLogout}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.background = '#FEF2F2';
                        e.currentTarget.style.color = '#DC2626';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.background = 'transparent';
                        e.currentTarget.style.color = '#EF4444';
                      }}
                    >
                      <LogOut size={16} />
                      Logout
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <a
                href="/login"
                style={{
                  ...navStyles.button,
                  ...navStyles.secondaryButton,
                }}
                onClick={(e) => {
                  e.preventDefault();
                  handleNavClick('/login');
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = '#667eea';
                  e.currentTarget.style.color = 'white';
                  e.currentTarget.style.borderColor = '#667eea';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'transparent';
                  e.currentTarget.style.color = '#667eea';
                  e.currentTarget.style.borderColor = '#e2e8f0';
                }}
              >
                <User size={16} />
                Login
              </a>
            )}

            {/* Mobile Menu Button */}
            {isMobile && (
              <button
                style={{
                  ...navStyles.mobileMenuButton,
                  display: 'flex',
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  setIsMobileMenuOpen(!isMobileMenuOpen);
                }}
              >
                {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            )}
          </div>
        </div>
      </nav>

      {/* Mobile Menu */}
      {isMobile && isMobileMenuOpen && (
        <div style={navStyles.mobileMenu}>
          {navItems.map((item) => {
            const Icon = item.icon;
            return (
              <a
                key={item.path}
                href={item.path}
                style={{
                  ...navStyles.navLink,
                  padding: '12px 0',
                  borderBottom: '1px solid #f1f5f9',
                }}
                onClick={(e) => {
                  e.preventDefault();
                  if (item.path.includes('#')) {
                    const element = document.querySelector(item.path.split('#')[1]);
                    element?.scrollIntoView({ behavior: 'smooth' });
                  } else {
                    handleNavClick(item.path);
                  }
                }}
              >
                <Icon size={16} />
                {item.label}
              </a>
            );
          })}

          {user && (
            <a
              href="/quiz"
              style={{
                ...navStyles.button,
                ...navStyles.primaryButton,
                justifyContent: 'center',
                marginTop: '16px',
              }}
              onClick={(e) => {
                e.preventDefault();
                handleNavClick('/quiz');
              }}
            >
              Start Quiz
            </a>
          )}
        </div>
      )}

      {/* Add responsive styles */}
      <style>{`
        @media (max-width: 768px) {
          .mobile-hidden { display: none !important; }
        }
      `}</style>

      {/* API Debugger - only in development */}
      {process.env.NODE_ENV === 'development' && <APIDebugger />}
    </>
  );
};

export default Header;
