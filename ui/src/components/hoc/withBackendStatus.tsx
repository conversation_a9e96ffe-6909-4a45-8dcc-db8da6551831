/**
 * Higher-Order Component for Backend Status Handling
 * 
 * Wraps components with backend status detection and provides
 * appropriate UX for offline/degraded scenarios.
 */

import React from 'react';
import { useBackendStatus } from '../../hooks/useBackendStatus';
import { BackendStatusDisplay, CookingLoader, OfflineMessage, DegradedPerformance } from '../ui/BackendStatusComponents';
import { isBackendDownError } from '../../services/errorHandler';

export interface WithBackendStatusOptions {
  /**
   * Show loading state when backend is being checked
   */
  showLoadingOnCheck?: boolean;
  
  /**
   * Show degraded performance warning
   */
  showDegradedWarning?: boolean;
  
  /**
   * Custom loading component
   */
  loadingComponent?: React.ComponentType;
  
  /**
   * Custom offline component
   */
  offlineComponent?: React.ComponentType<{ onRetry: () => void }>;
  
  /**
   * Custom degraded component
   */
  degradedComponent?: React.ComponentType<{ onRetry: () => void }>;
  
  /**
   * Fallback to original component on degraded performance
   */
  fallbackOnDegraded?: boolean;
  
  /**
   * Enable automatic retry
   */
  enableAutoRetry?: boolean;
}

const defaultOptions: WithBackendStatusOptions = {
  showLoadingOnCheck: true,
  showDegradedWarning: true,
  fallbackOnDegraded: true,
  enableAutoRetry: true,
};

/**
 * HOC that wraps a component with backend status handling
 */
export function withBackendStatus<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: WithBackendStatusOptions = {}
) {
  const opts = { ...defaultOptions, ...options };
  
  const WithBackendStatusComponent: React.FC<P> = (props) => {
    const backendStatus = useBackendStatus({
      enableAutoRetry: opts.enableAutoRetry,
    });

    // Show loading state when checking backend
    if (opts.showLoadingOnCheck && backendStatus.status === 'checking') {
      const LoadingComponent = opts.loadingComponent || CookingLoader;
      return <LoadingComponent />;
    }

    // Show offline message when backend is down
    if (backendStatus.status === 'offline') {
      const OfflineComponent = opts.offlineComponent || OfflineMessage;
      return <OfflineComponent onRetry={backendStatus.retry} />;
    }

    // Handle degraded performance
    if (backendStatus.status === 'degraded') {
      if (opts.fallbackOnDegraded) {
        // Show warning but still render the component
        const DegradedComponent = opts.degradedComponent || DegradedPerformance;
        return (
          <>
            {opts.showDegradedWarning && (
              <DegradedComponent onRetry={backendStatus.retry} />
            )}
            <WrappedComponent {...props} />
          </>
        );
      } else {
        // Show degraded component instead of wrapped component
        const DegradedComponent = opts.degradedComponent || DegradedPerformance;
        return <DegradedComponent onRetry={backendStatus.retry} />;
      }
    }

    // Backend is healthy, render the wrapped component
    return <WrappedComponent {...props} />;
  };

  WithBackendStatusComponent.displayName = `withBackendStatus(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithBackendStatusComponent;
}

/**
 * Hook-based alternative to HOC for more flexibility
 */
export function useBackendStatusWrapper(options: WithBackendStatusOptions = {}) {
  const opts = { ...defaultOptions, ...options };
  const backendStatus = useBackendStatus({
    enableAutoRetry: opts.enableAutoRetry,
  });

  const renderWithStatus = (children: React.ReactNode) => {
    // Show loading state when checking backend
    if (opts.showLoadingOnCheck && backendStatus.status === 'checking') {
      const LoadingComponent = opts.loadingComponent || CookingLoader;
      return <LoadingComponent />;
    }

    // Show offline message when backend is down
    if (backendStatus.status === 'offline') {
      const OfflineComponent = opts.offlineComponent || OfflineMessage;
      return <OfflineComponent onRetry={backendStatus.retry} />;
    }

    // Handle degraded performance
    if (backendStatus.status === 'degraded') {
      if (opts.fallbackOnDegraded) {
        // Show warning but still render children
        const DegradedComponent = opts.degradedComponent || DegradedPerformance;
        return (
          <>
            {opts.showDegradedWarning && (
              <DegradedComponent onRetry={backendStatus.retry} />
            )}
            {children}
          </>
        );
      } else {
        // Show degraded component instead of children
        const DegradedComponent = opts.degradedComponent || DegradedPerformance;
        return <DegradedComponent onRetry={backendStatus.retry} />;
      }
    }

    // Backend is healthy, render children
    return children;
  };

  return {
    ...backendStatus,
    renderWithStatus,
    shouldRenderChildren: backendStatus.status === 'online' || 
                         (backendStatus.status === 'degraded' && opts.fallbackOnDegraded),
  };
}

/**
 * Component wrapper for declarative usage
 */
interface BackendStatusWrapperProps extends WithBackendStatusOptions {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const BackendStatusWrapper: React.FC<BackendStatusWrapperProps> = ({
  children,
  fallback,
  ...options
}) => {
  const { renderWithStatus } = useBackendStatusWrapper(options);
  
  return (
    <>
      {renderWithStatus(children)}
      {fallback}
    </>
  );
};

/**
 * Error boundary that detects backend down errors
 */
interface BackendErrorBoundaryState {
  hasError: boolean;
  isBackendDown: boolean;
  error: Error | null;
}

export class BackendErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType<{ error: Error; retry: () => void }> },
  BackendErrorBoundaryState
> {
  constructor(props: any) {
    super(props);
    this.state = {
      hasError: false,
      isBackendDown: false,
      error: null,
    };
  }

  static getDerivedStateFromError(error: Error): BackendErrorBoundaryState {
    const isBackendDown = isBackendDownError(error);
    
    return {
      hasError: true,
      isBackendDown,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('BackendErrorBoundary caught an error:', error, errorInfo);
  }

  retry = () => {
    this.setState({
      hasError: false,
      isBackendDown: false,
      error: null,
    });
  };

  render() {
    if (this.state.hasError) {
      if (this.state.isBackendDown) {
        return <OfflineMessage onRetry={this.retry} />;
      }
      
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error!} retry={this.retry} />;
      }
      
      return <BackendStatusDisplay showRetry onRetry={this.retry} />;
    }

    return this.props.children;
  }
}

/**
 * Utility function to check if an error should trigger backend status handling
 */
export function shouldHandleAsBackendError(error: any): boolean {
  return isBackendDownError(error);
}

/**
 * React Query error handler that integrates with backend status
 */
export function createBackendAwareErrorHandler(
  onBackendDown?: () => void,
  onOtherError?: (error: any) => void
) {
  return (error: any) => {
    if (isBackendDownError(error)) {
      onBackendDown?.();
    } else {
      onOtherError?.(error);
    }
  };
}
