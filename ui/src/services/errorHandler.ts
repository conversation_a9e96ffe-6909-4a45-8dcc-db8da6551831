/**
 * Unified Error Handling Service
 * 
 * This service consolidates all error handling patterns across the frontend
 * to provide consistent error messaging, logging, and user feedback.
 */

import { toast } from "react-toastify";
import { ApiError } from "./APIServices";

// Global error reporter instance (will be set by the app)
let globalErrorReporter: ((error: any, message: string, context?: any) => void) | null = null;

export const setGlobalErrorReporter = (reporter: (error: any, message: string, context?: any) => void) => {
  globalErrorReporter = reporter;
};

// ============================================================================
// Types and Interfaces
// ============================================================================

export interface ErrorContext {
  action?: string;
  component?: string;
  userId?: string | number;
  sessionId?: string;
  metadata?: Record<string, any>;
  isBackendDown?: boolean;
}

export interface ErrorHandlerOptions {
  showToast?: boolean;
  logToConsole?: boolean;
  logLevel?: 'error' | 'warn' | 'info';
  customMessage?: string;
  fallbackMessage?: string;
  context?: ErrorContext;
}

export interface ApiErrorResponse {
  message?: string;
  detail?: string;
  errors?: Record<string, string[]>;
  status?: number;
}

// ============================================================================
// Error Classification
// ============================================================================

export enum ErrorType {
  NETWORK = 'NETWORK',
  API = 'API',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  SERVER = 'SERVER',
  CLIENT = 'CLIENT',
  UNKNOWN = 'UNKNOWN'
}

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// ============================================================================
// Error Classification Logic
// ============================================================================

function classifyError(error: any): { type: ErrorType; severity: ErrorSeverity; } {
  // Network errors
  if (!error.response && (error.code === 'NETWORK_ERROR' || error.message?.includes('Network'))) {
    return { type: ErrorType.NETWORK, severity: ErrorSeverity.HIGH };
  }

  // API errors with status codes
  if (error instanceof ApiError || error.response?.status) {
    const status = error.status || error.response?.status;

    switch (status) {
      case 400:
        return { type: ErrorType.VALIDATION, severity: ErrorSeverity.MEDIUM };
      case 401:
        return { type: ErrorType.AUTHENTICATION, severity: ErrorSeverity.HIGH };
      case 403:
        return { type: ErrorType.AUTHORIZATION, severity: ErrorSeverity.HIGH };
      case 404:
        return { type: ErrorType.NOT_FOUND, severity: ErrorSeverity.MEDIUM };
      case 422:
        return { type: ErrorType.VALIDATION, severity: ErrorSeverity.MEDIUM };
      case 500:
      case 502:
      case 503:
      case 504:
        return { type: ErrorType.SERVER, severity: ErrorSeverity.CRITICAL };
      default:
        return { type: ErrorType.API, severity: ErrorSeverity.MEDIUM };
    }
  }

  // Client-side errors
  if (error instanceof TypeError || error instanceof ReferenceError) {
    return { type: ErrorType.CLIENT, severity: ErrorSeverity.HIGH };
  }

  return { type: ErrorType.UNKNOWN, severity: ErrorSeverity.MEDIUM };
}

// ============================================================================
// Message Extraction and Formatting
// ============================================================================

function extractErrorMessage(error: any): string {
  // Handle ApiError instances
  if (error instanceof ApiError) {
    if (typeof error.detail === 'string') {
      return error.detail;
    }
    if (typeof error.detail === 'object' && error.detail && 'message' in error.detail) {
      return (error.detail as any).message;
    }
  }

  // Handle axios-style errors
  if (error.response?.data) {
    const data = error.response.data;

    // Try different message fields
    if (data.message) return data.message;
    if (data.detail) return data.detail;
    if (data.error) return data.error;

    // Handle validation errors
    if (data.errors && typeof data.errors === 'object') {
      const firstError = Object.values(data.errors)[0];
      if (Array.isArray(firstError) && firstError.length > 0) {
        return firstError[0];
      }
    }
  }

  // Handle standard Error objects
  if (error.message) {
    return error.message;
  }

  return 'An unexpected error occurred';
}

function formatErrorMessage(
  error: any,
  prefix?: string,
  customMessage?: string,
  fallbackMessage?: string
): string {
  if (customMessage) {
    return customMessage;
  }

  const baseMessage = extractErrorMessage(error);
  const message = baseMessage || fallbackMessage || 'An unexpected error occurred';

  return prefix ? `${prefix}: ${message}` : message;
}

// ============================================================================
// Logging Functions
// ============================================================================

function logError(
  error: any,
  context?: ErrorContext,
  level: 'error' | 'warn' | 'info' = 'error'
) {
  if (process.env.NODE_ENV !== 'development') {
    return; // Only log in development
  }

  const { type, severity } = classifyError(error);
  const message = extractErrorMessage(error);

  const logData = {
    message,
    type,
    severity,
    context,
    error: error.response || error,
    timestamp: new Date().toISOString(),
  };

  switch (level) {
    case 'error':
      console.error('🚨 Error:', logData);
      break;
    case 'warn':
      console.warn('⚠️ Warning:', logData);
      break;
    case 'info':
      console.info('ℹ️ Info:', logData);
      break;
  }
}

// ============================================================================
// Toast Notification Functions
// ============================================================================

function showErrorToast(message: string, type: ErrorType, severity: ErrorSeverity) {
  const toastOptions = {
    position: "top-right" as const,
    autoClose: getAutoCloseTime(severity),
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
  };

  switch (type) {
    case ErrorType.VALIDATION:
      toast.warn(message, toastOptions);
      break;
    case ErrorType.AUTHENTICATION:
    case ErrorType.AUTHORIZATION:
      toast.error(message, { ...toastOptions, autoClose: 8000 });
      break;
    case ErrorType.NETWORK:
    case ErrorType.SERVER:
      toast.error(message, { ...toastOptions, autoClose: 10000 });
      break;
    default:
      toast.error(message, toastOptions);
  }
}

function getAutoCloseTime(severity: ErrorSeverity): number {
  switch (severity) {
    case ErrorSeverity.LOW:
      return 3000;
    case ErrorSeverity.MEDIUM:
      return 5000;
    case ErrorSeverity.HIGH:
      return 8000;
    case ErrorSeverity.CRITICAL:
      return 10000;
    default:
      return 5000;
  }
}

// ============================================================================
// Main Error Handler
// ============================================================================

export function handleError(
  error: any,
  prefix?: string,
  options: ErrorHandlerOptions = {}
): void {
  const {
    showToast = true,
    logToConsole = true,
    logLevel = 'error',
    customMessage,
    fallbackMessage,
    context
  } = options;

  const { type, severity } = classifyError(error);
  const message = formatErrorMessage(error, prefix, customMessage, fallbackMessage);

  // Log the error
  if (logToConsole) {
    logError(error, context, logLevel);
  }

  // Show toast notification
  if (showToast) {
    showErrorToast(message, type, severity);
  }

  // Report to global error reporter (for development)
  if (globalErrorReporter && process.env.NODE_ENV === 'development') {
    globalErrorReporter(error, message, context);
  }
}

// ============================================================================
// Specialized Error Handlers
// ============================================================================

export function handleApiError(prefix: string, error: any, context?: ErrorContext): void {
  handleError(error, prefix, {
    showToast: true,
    logToConsole: true,
    context: { ...context, action: 'API_CALL' }
  });
}

export function handleNetworkError(error: any, context?: ErrorContext): void {
  // Check if this is a backend down scenario
  const isBackendDown = isBackendDownError(error);

  handleError(error, isBackendDown ? 'Backend Unavailable' : 'Network Error', {
    showToast: !isBackendDown, // Don't show toast for backend down (handled by components)
    logToConsole: true,
    fallbackMessage: isBackendDown
      ? 'Our servers are taking a quick break. Please try again in a moment.'
      : 'Please check your internet connection and try again',
    context: {
      ...context,
      action: 'NETWORK_REQUEST',
      isBackendDown
    }
  });
}

export function handleBackendDownError(error: any, context?: ErrorContext): void {
  handleError(error, 'Backend Temporarily Unavailable', {
    showToast: false, // Don't show toast, let components handle UX
    logToConsole: true,
    fallbackMessage: 'Our kitchen is preparing something amazing. Please try again in a moment.',
    context: { ...context, action: 'BACKEND_DOWN', isBackendDown: true }
  });
}

// Helper function to detect backend down scenarios
export function isBackendDownError(error: any): boolean {
  if (!error) return false;

  const errorMessage = error.message || error.toString() || '';
  const errorStatus = error.status || error.response?.status;

  // Network/fetch errors (backend completely unreachable)
  if (errorMessage.includes('fetch') ||
    errorMessage.includes('Network request failed') ||
    errorMessage.includes('Failed to fetch') ||
    errorMessage.includes('ERR_NETWORK') ||
    errorMessage.includes('ERR_INTERNET_DISCONNECTED') ||
    errorMessage.includes('timeout') ||
    errorMessage.includes('ECONNREFUSED') ||
    errorMessage.includes('ENOTFOUND')) {
    return true;
  }

  // Server errors that indicate backend is down
  if (errorStatus === 502 || // Bad Gateway
    errorStatus === 503 || // Service Unavailable
    errorStatus === 504) {  // Gateway Timeout
    return true;
  }

  return false;
}

export function handleValidationError(error: any, context?: ErrorContext): void {
  handleError(error, 'Validation Error', {
    showToast: true,
    logToConsole: false, // Validation errors are usually expected
    logLevel: 'warn',
    context: { ...context, action: 'VALIDATION' }
  });
}

export function handleAuthError(error: any, context?: ErrorContext): void {
  handleError(error, 'Authentication Error', {
    showToast: true,
    logToConsole: true,
    fallbackMessage: 'Please log in again to continue',
    context: { ...context, action: 'AUTHENTICATION' }
  });
}

// ============================================================================
// Utility Functions
// ============================================================================

export function isNetworkError(error: any): boolean {
  return !error.response && (
    error.code === 'NETWORK_ERROR' ||
    error.message?.includes('Network') ||
    error.message?.includes('fetch')
  );
}

export function isAuthError(error: any): boolean {
  const status = error.status || error.response?.status;
  return status === 401 || status === 403;
}

export function isValidationError(error: any): boolean {
  const status = error.status || error.response?.status;
  return status === 400 || status === 422;
}

// ============================================================================
// Success Message Handler
// ============================================================================

export function showSuccessMessage(
  message: string,
  autoClose: number = 3000
): void {
  toast.success(message, {
    position: "top-right",
    autoClose,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
  });
}

export function showInfoMessage(
  message: string,
  autoClose: number = 5000
): void {
  toast.info(message, {
    position: "top-right",
    autoClose,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
  });
}

// ============================================================================
// Default Export
// ============================================================================

const errorHandler = {
  handleError,
  handleApiError,
  handleNetworkError,
  handleValidationError,
  handleAuthError,
  showSuccessMessage,
  showInfoMessage,
  isNetworkError,
  isAuthError,
  isValidationError,
  ErrorType,
  ErrorSeverity
};

export default errorHandler;
