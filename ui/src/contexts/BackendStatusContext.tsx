/**
 * Backend Status Context
 * 
 * Provides global backend status state throughout the application
 * and coordinates with error handling and retry mechanisms.
 */

import React, { createContext, useContext, useEffect } from 'react';
import { useBackendStatus, BackendStatusInfo } from '../hooks/useBackendStatus';
import { useErrorContext } from './ErrorContext';
import { handleBackendDownError } from '../services/errorHandler';

interface BackendStatusContextValue extends BackendStatusInfo {
  retry: () => Promise<void>;
  getStatusMessage: () => string;
  getStatusColor: () => string;
  isBackendDown: boolean;
  isBackendDegraded: boolean;
  isBackendHealthy: boolean;
  hasConnectivityIssues: boolean;
}

const BackendStatusContext = createContext<BackendStatusContextValue | undefined>(undefined);

interface BackendStatusProviderProps {
  children: React.ReactNode;
  /**
   * Check interval in milliseconds
   */
  checkInterval?: number;
  /**
   * Enable automatic retry
   */
  enableAutoRetry?: boolean;
  /**
   * Maximum number of retries
   */
  maxRetries?: number;
}

export const BackendStatusProvider: React.FC<BackendStatusProviderProps> = ({
  children,
  checkInterval = 30000, // 30 seconds
  enableAutoRetry = true,
  maxRetries = 5,
}) => {
  const backendStatus = useBackendStatus({
    checkInterval,
    enableAutoRetry,
    maxRetries,
  });

  // Optional error context integration
  let addError: ((error: any) => void) | undefined;
  try {
    const errorContext = useErrorContext();
    addError = errorContext.addError;
  } catch (error) {
    // ErrorContext not available, continue without it
    console.warn('ErrorContext not available in BackendStatusProvider');
  }

  // Add backend errors to global error context
  useEffect(() => {
    if (!addError) return; // Skip if error context not available

    if (backendStatus.status === 'offline' && backendStatus.consecutiveFailures === 1) {
      // First time going offline
      addError({
        type: 'network',
        severity: 'critical',
        message: 'Backend is currently unavailable',
        context: {
          action: 'BACKEND_STATUS_CHECK',
          component: 'BackendStatusProvider',
        },
        isRetryable: true,
        retryAction: backendStatus.retry,
      });
    } else if (backendStatus.status === 'degraded' && backendStatus.consecutiveFailures === 1) {
      // First time going degraded
      addError({
        type: 'api',
        severity: 'medium',
        message: 'Backend is experiencing performance issues',
        context: {
          action: 'BACKEND_STATUS_CHECK',
          component: 'BackendStatusProvider',
        },
        isRetryable: true,
        retryAction: backendStatus.retry,
      });
    }
  }, [backendStatus.status, backendStatus.consecutiveFailures, addError, backendStatus.retry]);

  // Log status changes for debugging
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Backend Status Changed:', {
        status: backendStatus.status,
        consecutiveFailures: backendStatus.consecutiveFailures,
        estimatedDowntime: backendStatus.estimatedDowntime,
        lastChecked: backendStatus.lastChecked,
      });
    }
  }, [backendStatus.status, backendStatus.consecutiveFailures]);

  return (
    <BackendStatusContext.Provider value={backendStatus}>
      {children}
    </BackendStatusContext.Provider>
  );
};

/**
 * Hook to access backend status context
 */
export const useBackendStatusContext = (): BackendStatusContextValue => {
  const context = useContext(BackendStatusContext);
  if (context === undefined) {
    throw new Error('useBackendStatusContext must be used within a BackendStatusProvider');
  }
  return context;
};

/**
 * Hook to check if backend is available for a specific operation
 */
export const useBackendAvailability = () => {
  const { status, retry, isBackendDown, isBackendDegraded } = useBackendStatusContext();

  const canPerformOperation = (operationType: 'read' | 'write' | 'critical' = 'read') => {
    switch (operationType) {
      case 'critical':
        return status === 'online';
      case 'write':
        return status === 'online' || status === 'degraded';
      case 'read':
      default:
        return status !== 'offline';
    }
  };

  const getOperationMessage = (operationType: 'read' | 'write' | 'critical' = 'read') => {
    if (canPerformOperation(operationType)) {
      return null;
    }

    switch (operationType) {
      case 'critical':
        return isBackendDown
          ? 'This operation requires a stable connection. Please try again when we\'re back online.'
          : 'This operation is temporarily unavailable due to performance issues.';
      case 'write':
        return 'Unable to save changes right now. Please try again in a moment.';
      case 'read':
      default:
        return 'Unable to load data right now. Please try again in a moment.';
    }
  };

  return {
    canPerformOperation,
    getOperationMessage,
    retry,
    isBackendDown,
    isBackendDegraded,
    status,
  };
};

/**
 * HOC to provide backend status to any component
 */
export function withBackendStatusContext<P extends object>(
  Component: React.ComponentType<P & { backendStatus: BackendStatusContextValue; }>
) {
  const WithBackendStatusComponent: React.FC<P> = (props) => {
    const backendStatus = useBackendStatusContext();
    return <Component {...props} backendStatus={backendStatus} />;
  };

  WithBackendStatusComponent.displayName = `withBackendStatusContext(${Component.displayName || Component.name})`;

  return WithBackendStatusComponent;
}

/**
 * Component to display backend status indicator
 */
interface BackendStatusIndicatorProps {
  showWhenHealthy?: boolean;
  compact?: boolean;
  className?: string;
}

export const BackendStatusIndicator: React.FC<BackendStatusIndicatorProps> = ({
  showWhenHealthy = false,
  compact = true,
  className = '',
}) => {
  const { status, getStatusMessage, getStatusColor, retry, isRetrying } = useBackendStatusContext();

  if (!showWhenHealthy && status === 'online') {
    return null;
  }

  const statusColor = getStatusColor();
  const statusMessage = getStatusMessage();

  if (compact) {
    return (
      <div
        className={`d-flex align-items-center gap-2 ${className}`}
        style={{ fontSize: '0.875rem' }}
      >
        <div
          style={{
            width: '8px',
            height: '8px',
            borderRadius: '50%',
            backgroundColor: statusColor,
          }}
        />
        <span style={{ color: statusColor }}>{statusMessage}</span>
        {status !== 'online' && (
          <button
            className="btn btn-link btn-sm p-0"
            onClick={retry}
            disabled={isRetrying}
            style={{ fontSize: '0.75rem', color: statusColor }}
          >
            {isRetrying ? 'Checking...' : 'Retry'}
          </button>
        )}
      </div>
    );
  }

  return (
    <div className={`alert alert-${status === 'online' ? 'success' : status === 'offline' ? 'danger' : 'warning'} ${className}`}>
      <div className="d-flex align-items-center justify-content-between">
        <span>{statusMessage}</span>
        {status !== 'online' && (
          <button
            className="btn btn-outline-secondary btn-sm"
            onClick={retry}
            disabled={isRetrying}
          >
            {isRetrying ? 'Checking...' : 'Retry'}
          </button>
        )}
      </div>
    </div>
  );
};

export default BackendStatusProvider;
