#!/usr/bin/env python3
"""
Test runner script for Users API unit tests.

This script provides a convenient way to run users API tests locally
with various options and configurations.

Usage:
    python scripts/run_users_tests.py [options]

Examples:
    # Run all tests with coverage
    python scripts/run_users_tests.py

    # Run only model tests
    python scripts/run_users_tests.py --models

    # Run tests with verbose output
    python scripts/run_users_tests.py --verbose

    # Run tests without coverage
    python scripts/run_users_tests.py --no-coverage

    # Run specific test file
    python scripts/run_users_tests.py --file test_models.py

    # Run tests in parallel
    python scripts/run_users_tests.py --parallel

    # Generate HTML coverage report
    python scripts/run_users_tests.py --html-coverage
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path


def run_command(cmd, check=True, capture_output=False):
    """Run a shell command."""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, check=check, capture_output=capture_output, text=True)
    if capture_output:
        return result.stdout, result.stderr
    return result


def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = ["pytest", "pytest-django", "pytest-cov", "coverage"]

    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("Install them with: pip install -r requirements-test.txt")
        return False

    print("✅ All required packages are installed")
    return True


def setup_environment():
    """Set up test environment variables."""
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cosmetrics_ai.settings.testing")
    os.environ.setdefault("DATABASE_URL", "sqlite:///test.db")

    # Ensure we're in the project root
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)

    print(f"✅ Working directory: {os.getcwd()}")
    print(f"✅ Django settings: {os.environ.get('DJANGO_SETTINGS_MODULE')}")


def build_pytest_command(args):
    """Build the pytest command based on arguments."""
    cmd = ["python", "-m", "pytest"]

    # Test path
    if args.file:
        cmd.append(f"tests/users/{args.file}")
    else:
        cmd.append("tests/users/")

    # Coverage options
    if not args.no_coverage:
        cmd.extend(
            [
                "--cov=users",
                "--cov-report=term-missing",
                "--cov-fail-under=100",
                "--cov-branch",
            ]
        )

        if args.html_coverage:
            cmd.append("--cov-report=html:htmlcov")

        if args.xml_coverage:
            cmd.append("--cov-report=xml")

    # Verbosity
    if args.verbose:
        cmd.append("--verbose")
    elif args.quiet:
        cmd.append("--quiet")

    # Test markers
    if args.models:
        cmd.extend(["-m", "models"])
    elif args.api:
        cmd.extend(["-m", "api"])
    elif args.schemas:
        cmd.extend(["-m", "schemas"])
    elif args.backends:
        cmd.extend(["-m", "backends"])
    elif args.utils:
        cmd.extend(["-m", "utils"])
    elif args.slow:
        cmd.extend(["-m", "slow"])
    elif args.fast:
        cmd.extend(["-m", "not slow"])

    # Parallel execution
    if args.parallel:
        cmd.extend(["-n", "auto"])

    # Output format
    if args.junit_xml:
        cmd.extend(["--junit-xml=test-results.xml"])

    # Fail fast
    if args.fail_fast:
        cmd.append("--maxfail=1")

    # Show local variables in tracebacks
    if args.show_locals:
        cmd.append("--tb=long")
    else:
        cmd.append("--tb=short")

    # Disable warnings
    if args.disable_warnings:
        cmd.append("--disable-warnings")

    # Reuse database
    if not args.create_db:
        cmd.append("--reuse-db")

    # No migrations
    if args.no_migrations:
        cmd.append("--nomigrations")

    return cmd


def run_pre_test_checks():
    """Run pre-test checks."""
    print("🔍 Running pre-test checks...")

    # Check Django configuration
    try:
        run_command(["python", "manage.py", "check", "--deploy"], capture_output=True)
        print("✅ Django configuration check passed")
    except subprocess.CalledProcessError as e:
        print(f"❌ Django configuration check failed: {e}")
        return False

    # Check if migrations are up to date
    try:
        stdout, _ = run_command(["python", "manage.py", "showmigrations", "--plan"], capture_output=True)
        if "[ ]" in stdout:
            print("⚠️  Unapplied migrations detected")
            print("Run: python manage.py migrate")
        else:
            print("✅ All migrations are applied")
    except subprocess.CalledProcessError:
        print("⚠️  Could not check migration status")

    return True


def run_post_test_actions(args):
    """Run post-test actions."""
    if args.html_coverage and os.path.exists("htmlcov/index.html"):
        print(f"📊 HTML coverage report generated: file://{os.path.abspath('htmlcov/index.html')}")

    if args.open_coverage and os.path.exists("htmlcov/index.html"):
        import webbrowser

        webbrowser.open(f"file://{os.path.abspath('htmlcov/index.html')}")

    if args.xml_coverage and os.path.exists("coverage.xml"):
        print(f"📊 XML coverage report generated: {os.path.abspath('coverage.xml')}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Run Users API unit tests",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__,
    )

    # Test selection
    parser.add_argument("--file", help="Run specific test file")
    parser.add_argument("--models", action="store_true", help="Run only model tests")
    parser.add_argument("--api", action="store_true", help="Run only API tests")
    parser.add_argument("--schemas", action="store_true", help="Run only schema tests")
    parser.add_argument("--backends", action="store_true", help="Run only backend tests")
    parser.add_argument("--utils", action="store_true", help="Run only utility tests")
    parser.add_argument("--slow", action="store_true", help="Run only slow tests")
    parser.add_argument("--fast", action="store_true", help="Run only fast tests (exclude slow)")

    # Coverage options
    parser.add_argument("--no-coverage", action="store_true", help="Disable coverage reporting")
    parser.add_argument("--html-coverage", action="store_true", help="Generate HTML coverage report")
    parser.add_argument("--xml-coverage", action="store_true", help="Generate XML coverage report")
    parser.add_argument(
        "--open-coverage",
        action="store_true",
        help="Open HTML coverage report in browser",
    )

    # Output options
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--quiet", "-q", action="store_true", help="Quiet output")
    parser.add_argument("--junit-xml", action="store_true", help="Generate JUnit XML report")
    parser.add_argument("--show-locals", action="store_true", help="Show local variables in tracebacks")
    parser.add_argument("--disable-warnings", action="store_true", help="Disable warnings")

    # Execution options
    parser.add_argument("--parallel", action="store_true", help="Run tests in parallel")
    parser.add_argument("--fail-fast", action="store_true", help="Stop on first failure")
    parser.add_argument("--create-db", action="store_true", help="Create fresh test database")
    parser.add_argument("--no-migrations", action="store_true", help="Skip migrations")

    # Utility options
    parser.add_argument("--check-deps", action="store_true", help="Only check dependencies")
    parser.add_argument("--skip-checks", action="store_true", help="Skip pre-test checks")

    args = parser.parse_args()

    # Setup
    setup_environment()

    # Check dependencies
    if not check_dependencies():
        sys.exit(1)

    if args.check_deps:
        print("✅ Dependency check completed")
        sys.exit(0)

    # Pre-test checks
    if not args.skip_checks:
        if not run_pre_test_checks():
            sys.exit(1)

    # Build and run pytest command
    cmd = build_pytest_command(args)

    try:
        print("🧪 Running Users API tests...")
        print("=" * 60)

        result = run_command(cmd, check=False)

        print("=" * 60)

        if result.returncode == 0:
            print("✅ All tests passed!")
        else:
            print("❌ Some tests failed!")

        # Post-test actions
        run_post_test_actions(args)

        sys.exit(result.returncode)

    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
