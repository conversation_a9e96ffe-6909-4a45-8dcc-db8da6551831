"""
Unit tests for users API endpoints.
"""

import json
import os
from unittest.mock import patch

import django
import pytest

# Ensure Django is configured before importing Django components
if not hasattr(django.conf.settings, "configured") or not django.conf.settings.configured:
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cosmetrics_ai.settings.testing")
    django.setup()

from django.contrib.auth import get_user_model

from users.models import UserProfile

# Try to import Product model, use mock if not available
try:
    from products.models import Product
except ImportError:
    Product = None

User = get_user_model()


@pytest.mark.django_db
class TestPasswordResetEndpoint:
    """Test password reset functionality."""

    def test_password_reset_success(self, api_client, user, mock_token_generator):
        """Test successful password reset."""
        mock_token_generator.check_token.return_value = True

        data = {
            "uid": str(user.id),
            "token": "valid-token",
            "password": "newpassword123",
        }

        response = api_client.post(
            "/api/users/password-reset/confirm",
            data=json.dumps(data),
            content_type="application/json",
        )

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["message"] == "Password reset successful!"

        # Verify password was changed
        user.refresh_from_db()
        assert user.check_password("newpassword123")

    def test_password_reset_invalid_token(self, api_client, user, mock_token_generator):
        """Test password reset with invalid token."""
        mock_token_generator.check_token.return_value = False

        data = {
            "uid": str(user.id),
            "token": "invalid-token",
            "password": "newpassword123",
        }

        response = api_client.post(
            "/api/users/password-reset/confirm",
            data=json.dumps(data),
            content_type="application/json",
        )

        assert response.status_code == 400
        response_data = response.json()
        assert response_data["error"] == "Invalid or expired token"

    def test_password_reset_invalid_user(self, api_client, mock_token_generator):
        """Test password reset with invalid user ID."""
        data = {"uid": "99999", "token": "valid-token", "password": "newpassword123"}

        response = api_client.post(
            "/api/users/password-reset/confirm",
            data=json.dumps(data),
            content_type="application/json",
        )

        assert response.status_code == 400
        response_data = response.json()
        assert response_data["error"] == "Invalid request"

    @patch("users.api.logger")
    def test_password_reset_exception_handling(self, mock_logger, api_client):
        """Test password reset exception handling."""
        data = {"uid": "invalid", "token": "token", "password": "password"}

        response = api_client.post(
            "/api/users/password-reset/confirm",
            data=json.dumps(data),
            content_type="application/json",
        )

        assert response.status_code == 400
        mock_logger.error.assert_called()


@pytest.mark.django_db
class TestMeEndpoint:
    """Test current user endpoint."""

    def test_me_endpoint_authenticated(self, authenticated_client, user):
        """Test /me endpoint with authenticated user."""
        response = authenticated_client.get("/api/users/me")

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["email"] == user.email
        assert response_data["id"] == user.id
        assert response_data["first_name"] == user.first_name
        assert response_data["last_name"] == user.last_name

    def test_me_endpoint_unauthenticated(self, api_client):
        """Test /me endpoint without authentication."""
        response = api_client.get("/api/users/me")

        # Should return 401 or redirect to login
        assert response.status_code in [401, 403, 302]


@pytest.mark.django_db
class TestUserProfileEndpoints:
    """Test user profile CRUD endpoints."""

    def test_list_all_profiles(self, api_client, user_profile):
        """Test listing all user profiles."""
        response = api_client.get("/api/users/profiles/check/all")

        assert response.status_code == 200
        response_data = response.json()
        assert isinstance(response_data, list)
        assert len(response_data) >= 1

        # Check profile data structure
        profile_data = response_data[0]
        assert "id" in profile_data
        assert "user_id" in profile_data
        assert "first_name" in profile_data

    def test_create_user_profile(self, api_client, product):
        """Test creating a new user profile."""
        data = {
            "user_id": 999,
            "first_name": "New",
            "last_name": "Profile",
            "completed_questions": 3,
            "default_phone_number": "+1234567890",
            "default_street_address1": "123 Test St",
            "default_town_or_city": "Test City",
            "default_postcode": "12345",
            "default_country": "US",
            "hair_type": "2A",
            "questionaire_completed": False,
            "recommendations": [product.id],
        }

        response = api_client.post(
            "/api/users/profiles",
            data=json.dumps(data),
            content_type="application/json",
        )

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["first_name"] == "New"
        assert response_data["last_name"] == "Profile"

        # Verify user was created
        created_user = User.objects.get(email=f"user_{data['user_id']}@temp.com")
        assert created_user is not None

        # Verify profile was created
        profile = UserProfile.objects.get(user=created_user)
        assert profile.first_name == "New"
        assert product in profile.recommendations.all()

    def test_get_user_recommendations(self, api_client, user_profile, product):
        """Test getting user recommendations."""
        user_profile.recommendations.add(product)

        response = api_client.get(f"/api/users/profiles/recommendations/user/{user_profile.user.id}")

        assert response.status_code == 200
        response_data = response.json()
        assert isinstance(response_data, list)
        assert product.id in response_data

    def test_get_user_recommendations_not_found(self, api_client):
        """Test getting recommendations for non-existent user."""
        response = api_client.get("/api/users/profiles/recommendations/user/99999")

        assert response.status_code == 404

    def test_delete_user_profile_success(self, api_client, user_profile):
        """Test successful profile deletion."""
        user_id = user_profile.user.id

        response = api_client.delete(f"/api/users/profiles/user/{user_id}")

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is True

        # Verify profile was deleted
        assert not UserProfile.objects.filter(user__id=user_id).exists()

    def test_delete_user_profile_not_found(self, api_client):
        """Test deleting non-existent profile."""
        response = api_client.delete("/api/users/profiles/user/99999")

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is False


@pytest.mark.django_db
class TestUserCRUDEndpoints:
    """Test user CRUD endpoints."""

    def test_get_all_users(self, api_client, user):
        """Test getting all users."""
        response = api_client.get("/api/users/")

        assert response.status_code == 200
        response_data = response.json()
        assert isinstance(response_data, list)
        assert len(response_data) >= 1

        # Check user data structure
        user_data = next((u for u in response_data if u["id"] == user.id), None)
        assert user_data is not None
        assert user_data["email"] == user.email

    def test_get_user_by_id(self, api_client, user):
        """Test getting user by ID."""
        response = api_client.get(f"/api/users/id/{user.id}")

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["id"] == user.id
        assert response_data["email"] == user.email

    def test_get_user_by_id_not_found(self, api_client):
        """Test getting non-existent user by ID."""
        response = api_client.get("/api/users/id/99999")

        assert response.status_code == 404

    def test_get_user_by_email(self, api_client, user):
        """Test getting user by email."""
        response = api_client.get(f"/api/users/email/{user.email}")

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["email"] == user.email
        assert response_data["id"] == user.id

    def test_get_user_by_email_not_found(self, api_client):
        """Test getting non-existent user by email."""
        response = api_client.get("/api/users/email/<EMAIL>")

        assert response.status_code == 404


@pytest.mark.django_db
class TestAuthenticationEndpoints:
    """Test authentication endpoints."""

    def test_logout_authenticated(self, authenticated_client):
        """Test logout with authenticated user."""
        response = authenticated_client.post("/api/users/logout")

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["message"] == "Logged out successfully"

    def test_logout_unauthenticated(self, api_client):
        """Test logout without authentication."""
        response = api_client.post("/api/users/logout")

        # Should require authentication
        assert response.status_code in [401, 403, 302]

    def test_signup_success(self, api_client):
        """Test successful user signup."""
        data = {
            "email": "<EMAIL>",
            "password1": "signuppass123",
            "password2": "signuppass123",
            "first_name": "Signup",
            "last_name": "User",
        }

        response = api_client.put("/api/users/signup", data=json.dumps(data), content_type="application/json")

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["email"] == "<EMAIL>"

        # Verify user was created
        user = User.objects.get(email="<EMAIL>")
        assert user.first_name == "Signup"
        assert user.last_name == "User"

    def test_register_success(self, api_client):
        """Test successful user registration."""
        data = {
            "email": "<EMAIL>",
            "password": "registerpass123",
            "first_name": "Register",
            "last_name": "User",
        }

        response = api_client.post(
            "/api/users/register",
            data=json.dumps(data),
            content_type="application/json",
        )

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is True
        assert response_data["message"] == "User registered successfully"

        # Verify user was created
        user = User.objects.get(email="<EMAIL>")
        assert user.first_name == "Register"

    def test_register_duplicate_email(self, api_client, user):
        """Test registration with existing email."""
        data = {
            "email": user.email,
            "password": "newpass123",
            "first_name": "Duplicate",
            "last_name": "User",
        }

        response = api_client.post(
            "/api/users/register",
            data=json.dumps(data),
            content_type="application/json",
        )

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is False
        assert response_data["message"] == "Email already exists"

    @patch("users.api.validate_password")
    def test_register_invalid_password(self, mock_validate, api_client):
        """Test registration with invalid password."""
        from django.core.exceptions import ValidationError

        mock_validate.side_effect = ValidationError(["Password too weak"])

        data = {
            "email": "<EMAIL>",
            "password": "123",
            "first_name": "Weak",
            "last_name": "Password",
        }

        response = api_client.post(
            "/api/users/register",
            data=json.dumps(data),
            content_type="application/json",
        )

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is False
        assert "Password too weak" in response_data["message"]
