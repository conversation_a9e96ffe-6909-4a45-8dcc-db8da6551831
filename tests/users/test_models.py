"""
Unit tests for users models.
"""

import os

import django
import pytest

# Ensure Django is configured before importing Django components
if not hasattr(django.conf.settings, "configured") or not django.conf.settings.configured:
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cosmetrics_ai.settings.testing")
    django.setup()

from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from django.db import IntegrityError

from users.models import UserImage, UserProfile

# Try to import Product model, use mock if not available
try:
    from products.models import Product
except ImportError:
    Product = None

User = get_user_model()


@pytest.mark.django_db
class TestUserManager:
    """Test the custom UserManager."""

    def test_create_user_success(self):
        """Test successful user creation."""
        user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
        )

        assert user.email == "<EMAIL>"
        assert user.first_name == "Test"
        assert user.last_name == "User"
        assert user.check_password("testpass123")
        assert not user.is_staff
        assert not user.is_superuser
        assert user.is_active

    def test_create_user_without_email(self):
        """Test user creation fails without email."""
        with pytest.raises(ValueError, match="The Email field must be set"):
            User.objects.create_user(email="", password="testpass123")

    def test_create_user_with_none_email(self):
        """Test user creation fails with None email."""
        with pytest.raises(ValueError, match="The Email field must be set"):
            User.objects.create_user(email=None, password="testpass123")

    def test_create_user_normalizes_email(self):
        """Test email normalization."""
        user = User.objects.create_user(email="<EMAIL>", password="testpass123")
        assert user.email == "<EMAIL>"

    def test_create_superuser_success(self):
        """Test successful superuser creation."""
        user = User.objects.create_superuser(
            email="<EMAIL>",
            password="adminpass123",
            first_name="Admin",
            last_name="User",
        )

        assert user.email == "<EMAIL>"
        assert user.is_staff
        assert user.is_superuser
        assert user.is_active

    def test_create_superuser_without_is_staff(self):
        """Test superuser creation fails without is_staff=True."""
        with pytest.raises(ValueError, match="Superuser must have is_staff=True"):
            User.objects.create_superuser(email="<EMAIL>", password="adminpass123", is_staff=False)

    def test_create_superuser_without_is_superuser(self):
        """Test superuser creation fails without is_superuser=True."""
        with pytest.raises(ValueError, match="Superuser must have is_superuser=True"):
            User.objects.create_superuser(email="<EMAIL>", password="adminpass123", is_superuser=False)


@pytest.mark.django_db
class TestUser:
    """Test the custom User model."""

    def test_user_str_representation(self, user):
        """Test user string representation."""
        assert str(user) == user.email

    def test_user_email_unique(self, user):
        """Test email uniqueness constraint."""
        with pytest.raises(IntegrityError):
            User.objects.create_user(email=user.email, password="anotherpass123")

    def test_user_username_field(self):
        """Test USERNAME_FIELD is email."""
        assert User.USERNAME_FIELD == "email"

    def test_user_required_fields(self):
        """Test REQUIRED_FIELDS is empty (email is USERNAME_FIELD)."""
        assert User.REQUIRED_FIELDS == []

    def test_user_meta_options(self):
        """Test User model meta options."""
        assert User._meta.verbose_name == "User"
        assert User._meta.verbose_name_plural == "Users"
        assert User._meta.db_table == "auth_user"

    def test_user_full_name_property(self, user):
        """Test full name property."""
        expected_full_name = f"{user.first_name} {user.last_name}".strip()
        assert user.get_full_name() == expected_full_name

    def test_user_short_name_property(self, user):
        """Test short name property."""
        assert user.get_short_name() == user.first_name

    def test_user_without_password(self):
        """Test user creation without password."""
        user = User.objects.create_user(email="<EMAIL>")
        assert not user.has_usable_password()


@pytest.mark.django_db
class TestUserProfile:
    """Test the UserProfile model."""

    def test_user_profile_creation(self, user, user_profile_data):
        """Test user profile creation."""
        profile = UserProfile.objects.create(user=user, **user_profile_data)

        assert profile.user == user
        assert profile.first_name == user_profile_data["first_name"]
        assert profile.last_name == user_profile_data["last_name"]
        assert profile.completed_questions == user_profile_data["completed_questions"]
        assert profile.default_phone_number == user_profile_data["default_phone_number"]
        assert profile.hair_type == user_profile_data["hair_type"]
        assert profile.questionaire_completed == user_profile_data["questionaire_completed"]

    def test_user_profile_str_representation(self, user_profile):
        """Test user profile string representation."""
        expected_str = f"{user_profile.user.email} - Profile"
        assert str(user_profile) == expected_str

    def test_user_profile_default_values(self, user):
        """Test user profile default values."""
        profile = UserProfile.objects.create(user=user)

        assert profile.completed_questions == 0
        assert profile.questionaire_completed is False
        assert profile.first_name == ""
        assert profile.last_name == ""

    def test_user_profile_country_field(self, user):
        """Test country field functionality."""
        profile = UserProfile.objects.create(user=user, default_country="US")
        assert profile.default_country == "US"

    def test_user_profile_recommendations_relationship(self, user_profile, product):
        """Test many-to-many relationship with products."""
        if Product is None:
            pytest.skip("Product model not available")

        user_profile.recommendations.add(product)

        assert product in user_profile.recommendations.all()
        assert user_profile in product.recommended_users.all()

    def test_user_profile_indexes(self):
        """Test database indexes are properly defined."""
        indexes = UserProfile._meta.indexes
        index_fields = [list(index.fields) for index in indexes]

        assert ["user", "questionaire_completed"] in index_fields
        assert ["completed_questions"] in index_fields
        assert ["hair_type"] in index_fields

    def test_user_profile_cascade_delete(self, user_profile):
        """Test profile is deleted when user is deleted."""
        user = user_profile.user
        user_id = user.id
        profile_id = user_profile.id

        user.delete()

        assert not User.objects.filter(id=user_id).exists()
        assert not UserProfile.objects.filter(id=profile_id).exists()


@pytest.mark.django_db
class TestUserImage:
    """Test the UserImage model."""

    def test_user_image_creation(self, user):
        """Test user image creation."""
        image_file = SimpleUploadedFile("test.jpg", b"fake image content", content_type="image/jpeg")

        user_image = UserImage.objects.create(user=user, image=image_file)

        assert user_image.user == user
        assert user_image.image is not None

    def test_user_image_without_image(self, user):
        """Test user image creation without image file."""
        user_image = UserImage.objects.create(user=user)

        assert user_image.user == user
        assert not user_image.image

    def test_user_image_cascade_delete(self, user_image):
        """Test image is deleted when user is deleted."""
        user = user_image.user
        user_id = user.id
        image_id = user_image.id

        user.delete()

        assert not User.objects.filter(id=user_id).exists()
        assert not UserImage.objects.filter(id=image_id).exists()

    def test_multiple_images_per_user(self, user):
        """Test user can have multiple images."""
        image1 = UserImage.objects.create(user=user)
        image2 = UserImage.objects.create(user=user)

        user_images = UserImage.objects.filter(user=user)
        assert image1 in user_images
        assert image2 in user_images
        assert user_images.count() == 2


@pytest.mark.django_db
class TestUserProfileSignals:
    """Test user profile signal handlers."""

    def test_user_profile_auto_creation_signal_exists(self):
        """Test that the signal for auto-creating profiles exists."""
        # This test verifies the signal is connected
        # The actual signal is commented out in the models.py
        # but we test the structure is in place
        from django.db.models.signals import post_save

        from users.models import User

        # Check if any post_save signals are connected to User
        signals = post_save._live_receivers(sender=User)
        # Since the signal is commented out, this should be empty or minimal
        assert isinstance(signals, list)


@pytest.mark.django_db
class TestModelValidation:
    """Test model field validation."""

    def test_user_email_validation(self):
        """Test email field validation."""
        # Test valid email
        user = User(email="<EMAIL>", password="testpass123")
        user.full_clean()  # Should not raise

        # Test invalid email format would be handled by Django's EmailField
        # but we can test empty email through the manager
        with pytest.raises(ValueError):
            User.objects.create_user(email="", password="testpass123")

    def test_user_profile_phone_number_field(self, user):
        """Test phone number field accepts various formats."""
        profile = UserProfile.objects.create(user=user, default_phone_number="******-567-8900")
        assert profile.default_phone_number == "******-567-8900"

    def test_user_profile_hair_type_field(self, user):
        """Test hair type field accepts valid values."""
        valid_hair_types = ["1A", "2B", "3C", "4A"]

        for hair_type in valid_hair_types:
            profile = UserProfile.objects.create(user=user, hair_type=hair_type)
            assert profile.hair_type == hair_type
            profile.delete()  # Clean up for next iteration
