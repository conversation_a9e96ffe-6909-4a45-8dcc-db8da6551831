"""
Unit tests for users authentication backends.
"""

import os
from unittest.mock import patch

import django
import pytest

# Ensure Django is configured before importing Django components
if not hasattr(django.conf.settings, "configured") or not django.conf.settings.configured:
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cosmetrics_ai.settings.testing")
    django.setup()

from django.contrib.auth import get_user_model
from django.test import RequestFactory

from users.backends import EmailBackend

User = get_user_model()


@pytest.mark.django_db
class TestEmailBackend:
    """Test the EmailBackend authentication backend."""

    def setup_method(self):
        """Set up test method."""
        self.backend = EmailBackend()
        self.factory = RequestFactory()

    def test_authenticate_with_valid_credentials(self, user):
        """Test authentication with valid email and password."""
        request = self.factory.post("/login")

        authenticated_user = self.backend.authenticate(request=request, username=user.email, password="testpass123")

        assert authenticated_user == user

    def test_authenticate_with_invalid_password(self, user):
        """Test authentication with invalid password."""
        request = self.factory.post("/login")

        authenticated_user = self.backend.authenticate(request=request, username=user.email, password="wrongpassword")

        assert authenticated_user is None

    def test_authenticate_with_nonexistent_user(self):
        """Test authentication with non-existent user."""
        request = self.factory.post("/login")

        authenticated_user = self.backend.authenticate(
            request=request, username="<EMAIL>", password="anypassword"
        )

        assert authenticated_user is None

    def test_authenticate_with_none_username(self):
        """Test authentication with None username."""
        request = self.factory.post("/login")

        authenticated_user = self.backend.authenticate(request=request, username=None, password="anypassword")

        assert authenticated_user is None

    def test_authenticate_with_none_password(self, user):
        """Test authentication with None password."""
        request = self.factory.post("/login")

        authenticated_user = self.backend.authenticate(request=request, username=user.email, password=None)

        assert authenticated_user is None

    def test_authenticate_with_empty_credentials(self):
        """Test authentication with empty credentials."""
        request = self.factory.post("/login")

        authenticated_user = self.backend.authenticate(request=request, username="", password="")

        assert authenticated_user is None

    def test_authenticate_case_insensitive_email(self, user):
        """Test authentication with case-insensitive email."""
        request = self.factory.post("/login")

        # Test with uppercase email
        authenticated_user = self.backend.authenticate(
            request=request, username=user.email.upper(), password="testpass123"
        )

        assert authenticated_user == user

    def test_authenticate_with_inactive_user(self, user):
        """Test authentication with inactive user."""
        user.is_active = False
        user.save()

        request = self.factory.post("/login")

        authenticated_user = self.backend.authenticate(request=request, username=user.email, password="testpass123")

        # Should return None for inactive users
        assert authenticated_user is None

    def test_get_user_valid_id(self, user):
        """Test get_user with valid user ID."""
        retrieved_user = self.backend.get_user(user.id)
        assert retrieved_user == user

    def test_get_user_invalid_id(self):
        """Test get_user with invalid user ID."""
        retrieved_user = self.backend.get_user(99999)
        assert retrieved_user is None

    def test_get_user_none_id(self):
        """Test get_user with None ID."""
        retrieved_user = self.backend.get_user(None)
        assert retrieved_user is None

    def test_get_user_string_id(self, user):
        """Test get_user with string ID."""
        retrieved_user = self.backend.get_user(str(user.id))
        assert retrieved_user == user

    def test_get_user_invalid_string_id(self):
        """Test get_user with invalid string ID."""
        retrieved_user = self.backend.get_user("invalid")
        assert retrieved_user is None

    @patch("users.backends.User.objects.get")
    def test_authenticate_database_error(self, mock_get):
        """Test authentication with database error."""
        mock_get.side_effect = Exception("Database error")

        request = self.factory.post("/login")

        authenticated_user = self.backend.authenticate(
            request=request, username="<EMAIL>", password="testpass123"
        )

        assert authenticated_user is None

    @patch("users.backends.User.objects.get")
    def test_get_user_database_error(self, mock_get):
        """Test get_user with database error."""
        mock_get.side_effect = Exception("Database error")

        retrieved_user = self.backend.get_user(1)
        assert retrieved_user is None

    def test_authenticate_with_user_does_not_exist_exception(self):
        """Test authenticate handles User.DoesNotExist properly."""
        request = self.factory.post("/login")

        # This should not raise an exception
        authenticated_user = self.backend.authenticate(
            request=request, username="<EMAIL>", password="anypassword"
        )

        assert authenticated_user is None

    def test_authenticate_with_multiple_users_returned(self):
        """Test authenticate when multiple users might be returned."""
        # Create two users with same email (shouldn't happen in practice due to unique constraint)
        # but test the exception handling
        request = self.factory.post("/login")

        with patch("users.backends.User.objects.get") as mock_get:
            mock_get.side_effect = User.MultipleObjectsReturned("Multiple users found")

            authenticated_user = self.backend.authenticate(
                request=request, username="<EMAIL>", password="testpass123"
            )

            assert authenticated_user is None

    def test_authenticate_password_check_exception(self, user):
        """Test authenticate when password check raises exception."""
        request = self.factory.post("/login")

        with patch.object(user, "check_password") as mock_check:
            mock_check.side_effect = Exception("Password check error")

            authenticated_user = self.backend.authenticate(request=request, username=user.email, password="testpass123")

            assert authenticated_user is None

    def test_backend_supports_object_permissions(self):
        """Test that backend doesn't support object permissions."""
        # EmailBackend should not support object permissions
        assert not hasattr(self.backend, "has_perm") or not hasattr(self.backend, "has_module_perms")

    def test_backend_supports_anonymous_user(self):
        """Test backend behavior with anonymous user."""
        # get_user should handle anonymous user (None ID)
        retrieved_user = self.backend.get_user(None)
        assert retrieved_user is None

    def test_authenticate_with_kwargs_only(self, user):
        """Test authenticate with keyword arguments only."""
        request = self.factory.post("/login")

        authenticated_user = self.backend.authenticate(
            request=request,
            email=user.email,  # Using email kwarg instead of username
            password="testpass123",
        )

        # Should handle email kwarg if implemented
        # If not implemented, should return None gracefully
        assert authenticated_user is None or authenticated_user == user

    def test_authenticate_missing_request(self, user):
        """Test authenticate without request parameter."""
        # Some authentication backends might be called without request
        authenticated_user = self.backend.authenticate(username=user.email, password="testpass123")

        # Should handle missing request gracefully
        assert authenticated_user == user or authenticated_user is None

    def test_authenticate_with_extra_kwargs(self, user):
        """Test authenticate with extra keyword arguments."""
        request = self.factory.post("/login")

        authenticated_user = self.backend.authenticate(
            request=request,
            username=user.email,
            password="testpass123",
            extra_param="should_be_ignored",
        )

        # Should ignore extra parameters and work normally
        assert authenticated_user == user

    def test_get_user_with_deleted_user(self):
        """Test get_user with ID of deleted user."""
        # Create and delete a user
        user = User.objects.create_user(email="<EMAIL>", password="testpass123")
        user_id = user.id
        user.delete()

        retrieved_user = self.backend.get_user(user_id)
        assert retrieved_user is None

    def test_authenticate_preserves_user_state(self, user):
        """Test that authentication doesn't modify user state."""
        original_last_login = user.last_login
        original_is_active = user.is_active

        request = self.factory.post("/login")

        authenticated_user = self.backend.authenticate(request=request, username=user.email, password="testpass123")

        # Refresh user from database
        user.refresh_from_db()

        # Authentication should not modify user state
        assert user.last_login == original_last_login
        assert user.is_active == original_is_active
        assert authenticated_user == user
