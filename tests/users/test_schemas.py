"""
Unit tests for users schemas.
"""

import os

import django
import pytest
from pydantic import ValidationError

# Ensure Django is configured before importing Django components
if not hasattr(django.conf.settings, "configured") or not django.conf.settings.configured:
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cosmetrics_ai.settings.testing")
    django.setup()

from django.contrib.auth import get_user_model

# Try to import schemas, skip tests if not available
try:
    from users.schemas import (
        CurrentUserSchema,
        LoginSchema,
        PasswordResetConfirmSchema,
        PasswordResetRequestSchema,
        RegisterSchema,
        UserIn,
        UserProfileCreateSchema,
        UserProfileInSchema,
        UserProfileOutSchema,
        UserSchema,
    )

    SCHEMAS_AVAILABLE = True
except ImportError:
    SCHEMAS_AVAILABLE = False


User = get_user_model()


@pytest.mark.django_db
@pytest.mark.skipif(not SCHEMAS_AVAILABLE, reason="Schemas not available")
class TestCurrentUserSchema:
    """Test CurrentUserSchema."""

    def test_current_user_schema_valid(self, user):
        """Test valid current user schema."""
        schema = CurrentUserSchema(
            email=user.email,
            id=user.id,
            first_name=user.first_name,
            last_name=user.last_name,
        )

        assert schema.email == user.email
        assert schema.id == user.id
        assert schema.first_name == user.first_name
        assert schema.last_name == user.last_name

    def test_current_user_schema_defaults(self):
        """Test current user schema with default values."""
        schema = CurrentUserSchema(email="<EMAIL>", id=1)

        assert schema.email == "<EMAIL>"
        assert schema.id == 1
        assert schema.first_name == ""
        assert schema.last_name == ""


@pytest.mark.django_db
@pytest.mark.skipif(not SCHEMAS_AVAILABLE, reason="Schemas not available")
class TestUserSchema:
    """Test UserSchema."""

    def test_user_schema_from_orm(self, user):
        """Test UserSchema creation from ORM object."""
        schema = UserSchema.from_orm(user)

        assert schema.id == user.id
        assert schema.email == user.email
        assert schema.first_name == user.first_name
        assert schema.last_name == user.last_name
        assert schema.is_staff == user.is_staff
        assert schema.is_active == user.is_active
        assert schema.date_joined == user.date_joined

    def test_user_schema_fields(self):
        """Test UserSchema includes correct fields."""
        expected_fields = [
            "id",
            "email",
            "first_name",
            "last_name",
            "is_staff",
            "is_active",
            "date_joined",
        ]

        for field in expected_fields:
            assert field in UserSchema.__fields__


class TestUserIn:
    """Test UserIn schema."""

    def test_user_in_valid(self):
        """Test valid UserIn schema."""
        schema = UserIn(
            email="<EMAIL>",
            password1="testpass123",
            password2="testpass123",
            first_name="Test",
            last_name="User",
        )

        assert schema.email == "<EMAIL>"
        assert schema.password1 == "testpass123"
        assert schema.password2 == "testpass123"
        assert schema.first_name == "Test"
        assert schema.last_name == "User"

    def test_user_in_defaults(self):
        """Test UserIn schema with default values."""
        schema = UserIn(email="<EMAIL>", password1="pass1", password2="pass2")

        assert schema.first_name == ""
        assert schema.last_name == ""

    def test_user_in_required_fields(self):
        """Test UserIn required fields."""
        with pytest.raises(ValidationError):
            UserIn()  # Missing required fields


class TestLoginSchema:
    """Test LoginSchema."""

    def test_login_schema_with_email(self):
        """Test LoginSchema with email."""
        schema = LoginSchema(email="<EMAIL>", password="testpass123")

        assert schema.email == "<EMAIL>"
        assert schema.password == "testpass123"

    def test_login_schema_with_username_fallback(self):
        """Test LoginSchema with username fallback to email."""
        schema = LoginSchema(username="<EMAIL>", password="testpass123")

        assert schema.email == "<EMAIL>"
        assert schema.password == "testpass123"

    def test_login_schema_username_and_email(self):
        """Test LoginSchema with both username and email."""
        schema = LoginSchema(
            email="<EMAIL>",
            username="<EMAIL>",
            password="testpass123",
        )

        # Email should take precedence
        assert schema.email == "<EMAIL>"
        assert schema.password == "testpass123"

    def test_login_schema_required_password(self):
        """Test LoginSchema requires password."""
        with pytest.raises(ValidationError):
            LoginSchema(email="<EMAIL>")


class TestRegisterSchema:
    """Test RegisterSchema."""

    def test_register_schema_valid(self):
        """Test valid RegisterSchema."""
        schema = RegisterSchema(
            email="<EMAIL>",
            password="registerpass123",
            first_name="Register",
            last_name="User",
        )

        assert schema.email == "<EMAIL>"
        assert schema.password == "registerpass123"
        assert schema.first_name == "Register"
        assert schema.last_name == "User"

    def test_register_schema_defaults(self):
        """Test RegisterSchema with default values."""
        schema = RegisterSchema(email="<EMAIL>", password="registerpass123")

        assert schema.first_name == ""
        assert schema.last_name == ""

    def test_register_schema_required_fields(self):
        """Test RegisterSchema required fields."""
        with pytest.raises(ValidationError):
            RegisterSchema()  # Missing required fields

        with pytest.raises(ValidationError):
            RegisterSchema(email="<EMAIL>")  # Missing password


class TestPasswordResetSchemas:
    """Test password reset schemas."""

    def test_password_reset_request_schema(self):
        """Test PasswordResetRequestSchema."""
        schema = PasswordResetRequestSchema(email="<EMAIL>")

        assert schema.email == "<EMAIL>"

    def test_password_reset_confirm_schema(self):
        """Test PasswordResetConfirmSchema."""
        schema = PasswordResetConfirmSchema(uid="123", token="reset-token", password="newpassword123")

        assert schema.uid == "123"
        assert schema.token == "reset-token"
        assert schema.password == "newpassword123"

    def test_password_reset_confirm_required_fields(self):
        """Test PasswordResetConfirmSchema required fields."""
        with pytest.raises(ValidationError):
            PasswordResetConfirmSchema()


@pytest.mark.django_db
class TestUserProfileSchemas:
    """Test user profile schemas."""

    def test_user_profile_out_schema_from_django(self, user_profile, product):
        """Test UserProfileOutSchema from Django model."""
        user_profile.recommendations.add(product)

        schema = UserProfileOutSchema.from_django(user_profile)

        assert schema.id == user_profile.id
        assert schema.user_id == user_profile.user.id
        assert schema.first_name == user_profile.first_name
        assert schema.last_name == user_profile.last_name
        assert schema.completed_questions == user_profile.completed_questions
        assert schema.questionaire_completed == user_profile.questionaire_completed
        assert product.id in schema.recommendations

    def test_user_profile_in_schema_valid(self):
        """Test valid UserProfileInSchema."""
        schema = UserProfileInSchema(
            user_id=1,
            first_name="Profile",
            last_name="User",
            completed_questions=5,
            default_phone_number="+1234567890",
            default_street_address1="123 Test St",
            default_town_or_city="Test City",
            default_postcode="12345",
            default_country="US",
            hair_type="2A",
            questionaire_completed=True,
            recommendations=[1, 2, 3],
        )

        assert schema.user_id == 1
        assert schema.first_name == "Profile"
        assert schema.completed_questions == 5
        assert schema.hair_type == "2A"
        assert schema.questionaire_completed is True
        assert schema.recommendations == [1, 2, 3]

    def test_user_profile_in_schema_defaults(self):
        """Test UserProfileInSchema with default values."""
        schema = UserProfileInSchema(user_id=1, first_name="Test", last_name="User")

        assert schema.completed_questions == 0
        assert schema.questionaire_completed is False
        assert schema.recommendations == []
        assert schema.default_phone_number is None

    def test_user_profile_create_schema_valid(self, product):
        """Test valid UserProfileCreateSchema."""
        schema = UserProfileCreateSchema(
            user_id=999,
            first_name="Create",
            last_name="User",
            completed_questions=3,
            recommendations=[product.id],
        )

        assert schema.user_id == 999
        assert schema.first_name == "Create"
        assert schema.recommendations == [product.id]

    def test_user_profile_create_schema_to_django_model(self, product):
        """Test UserProfileCreateSchema to_django_model method."""
        schema = UserProfileCreateSchema(
            user_id=999,
            first_name="Create",
            last_name="User",
            completed_questions=3,
            default_phone_number="+1234567890",
            recommendations=[product.id],
        )

        django_data = schema.to_django_model()

        # Should exclude user_id and recommendations
        assert "user_id" not in django_data
        assert "recommendations" not in django_data
        assert django_data["first_name"] == "Create"
        assert django_data["last_name"] == "User"
        assert django_data["completed_questions"] == 3
        assert django_data["default_phone_number"] == "+1234567890"


class TestSchemaValidation:
    """Test schema validation edge cases."""

    def test_email_validation(self):
        """Test email field validation."""
        # Valid email
        schema = RegisterSchema(email="<EMAIL>", password="testpass123")
        assert schema.email == "<EMAIL>"

        # Invalid email should be caught by Pydantic
        with pytest.raises(ValidationError):
            RegisterSchema(email="invalid-email", password="testpass123")

    def test_optional_fields_none_values(self):
        """Test optional fields with None values."""
        schema = UserProfileInSchema(
            user_id=1,
            first_name="Test",
            last_name="User",
            default_phone_number=None,
            default_country=None,
            hair_type=None,
        )

        assert schema.default_phone_number is None
        assert schema.default_country is None
        assert schema.hair_type is None

    def test_list_field_validation(self):
        """Test list field validation."""
        # Valid list
        schema = UserProfileInSchema(user_id=1, first_name="Test", last_name="User", recommendations=[1, 2, 3])
        assert schema.recommendations == [1, 2, 3]

        # Empty list
        schema = UserProfileInSchema(user_id=1, first_name="Test", last_name="User", recommendations=[])
        assert schema.recommendations == []
