"""
Basic setup tests to verify Django configuration.
"""

import os

import django
import pytest

# Ensure Django is configured before importing Django components
if not hasattr(django.conf.settings, "configured") or not django.conf.settings.configured:
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cosmetrics_ai.settings.testing")
    django.setup()

from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from django.test import Client

from users.models import UserImage, UserProfile

User = get_user_model()


@pytest.mark.django_db
class TestBasicSetup:
    """Test basic Django setup and configuration."""

    def test_django_setup(self):
        """Test that Django is properly configured."""
        from django.conf import settings

        assert settings.configured
        assert settings.SECRET_KEY is not None

    def test_user_model_import(self):
        """Test that User model can be imported."""
        assert User is not None
        assert hasattr(User, "objects")

    def test_user_profile_model_import(self):
        """Test that UserProfile model can be imported."""
        assert UserProfile is not None
        assert hasattr(UserProfile, "objects")

    def test_user_image_model_import(self):
        """Test that UserImage model can be imported."""
        assert UserImage is not None
        assert hasattr(UserImage, "objects")

    def test_database_connection(self):
        """Test database connection."""
        from django.db import connection

        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            assert result == (1,)

    def test_user_creation(self):
        """Test basic user creation."""
        user = User.objects.create_user(email="<EMAIL>", password="testpass123")
        assert user.email == "<EMAIL>"
        assert user.check_password("testpass123")

    def test_user_profile_creation(self):
        """Test basic user profile creation."""
        user = User.objects.create_user(email="<EMAIL>", password="testpass123")
        profile = UserProfile.objects.create(user=user, first_name="Test", last_name="User")
        assert profile.user == user
        assert profile.first_name == "Test"

    def test_user_image_creation(self):
        """Test basic user image creation."""
        user = User.objects.create_user(email="<EMAIL>", password="testpass123")
        image_file = SimpleUploadedFile("test.jpg", b"fake image content", content_type="image/jpeg")
        user_image = UserImage.objects.create(user=user, image=image_file)
        assert user_image.user == user

    def test_client_creation(self):
        """Test that test client can be created."""
        client = Client()
        assert client is not None

    def test_authentication_backend_import(self):
        """Test that authentication backend can be imported."""
        try:
            from users.backends import EmailBackend

            assert EmailBackend is not None
        except ImportError:
            pytest.skip("EmailBackend not available")

    def test_settings_configuration(self):
        """Test that test settings are properly configured."""
        from django.conf import settings

        # Check that we're using the testing configuration
        assert "testing" in settings.SETTINGS_MODULE or settings.DEBUG is True

        # Check database configuration
        assert "default" in settings.DATABASES

        # Check that custom user model is configured
        assert settings.AUTH_USER_MODEL == "users.User"

    def test_apps_installed(self):
        """Test that required apps are installed."""
        from django.conf import settings

        required_apps = [
            "django.contrib.auth",
            "django.contrib.contenttypes",
            "users",
        ]

        for app in required_apps:
            assert app in settings.INSTALLED_APPS

    def test_middleware_configuration(self):
        """Test that required middleware is configured."""
        from django.conf import settings

        required_middleware = [
            "django.contrib.auth.middleware.AuthenticationMiddleware",
            "django.contrib.sessions.middleware.SessionMiddleware",
        ]

        for middleware in required_middleware:
            assert middleware in settings.MIDDLEWARE

    def test_password_hashers(self):
        """Test that password hashers are configured."""
        from django.conf import settings

        assert hasattr(settings, "PASSWORD_HASHERS")
        assert len(settings.PASSWORD_HASHERS) > 0

    def test_email_backend(self):
        """Test that email backend is configured for testing."""
        from django.conf import settings

        # Should be using locmem or console backend for testing
        assert "locmem" in settings.EMAIL_BACKEND or "console" in settings.EMAIL_BACKEND

    def test_cache_configuration(self):
        """Test that cache is configured."""
        from django.conf import settings

        assert "default" in settings.CACHES

    def test_static_files_configuration(self):
        """Test that static files are configured."""
        from django.conf import settings

        assert hasattr(settings, "STATIC_URL")
        assert hasattr(settings, "STATIC_ROOT")

    def test_media_files_configuration(self):
        """Test that media files are configured."""
        from django.conf import settings

        assert hasattr(settings, "MEDIA_URL")
        assert hasattr(settings, "MEDIA_ROOT")

    def test_timezone_configuration(self):
        """Test that timezone is configured."""
        from django.conf import settings

        assert hasattr(settings, "TIME_ZONE")
        assert hasattr(settings, "USE_TZ")

    def test_internationalization_configuration(self):
        """Test that internationalization is configured."""
        from django.conf import settings

        assert hasattr(settings, "LANGUAGE_CODE")
        assert hasattr(settings, "USE_I18N")


@pytest.mark.django_db
class TestDatabaseOperations:
    """Test basic database operations."""

    def test_user_crud_operations(self):
        """Test basic CRUD operations on User model."""
        # Create
        user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="CRUD",
            last_name="Test",
        )
        assert user.id is not None

        # Read
        retrieved_user = User.objects.get(email="<EMAIL>")
        assert retrieved_user.email == user.email

        # Update
        retrieved_user.first_name = "Updated"
        retrieved_user.save()
        updated_user = User.objects.get(id=user.id)
        assert updated_user.first_name == "Updated"

        # Delete
        user_id = user.id
        user.delete()
        assert not User.objects.filter(id=user_id).exists()

    def test_user_profile_crud_operations(self):
        """Test basic CRUD operations on UserProfile model."""
        # Create user first
        user = User.objects.create_user(email="<EMAIL>", password="testpass123")

        # Create profile
        profile = UserProfile.objects.create(user=user, first_name="Profile", last_name="CRUD", completed_questions=5)
        assert profile.id is not None

        # Read
        retrieved_profile = UserProfile.objects.get(user=user)
        assert retrieved_profile.first_name == "Profile"

        # Update
        retrieved_profile.completed_questions = 10
        retrieved_profile.save()
        updated_profile = UserProfile.objects.get(id=profile.id)
        assert updated_profile.completed_questions == 10

        # Delete (cascade from user)
        user.delete()
        assert not UserProfile.objects.filter(id=profile.id).exists()

    def test_user_image_crud_operations(self):
        """Test basic CRUD operations on UserImage model."""
        # Create user first
        user = User.objects.create_user(email="<EMAIL>", password="testpass123")

        # Create image
        image_file = SimpleUploadedFile("crud_test.jpg", b"fake image content", content_type="image/jpeg")
        user_image = UserImage.objects.create(user=user, image=image_file)
        assert user_image.id is not None

        # Read
        retrieved_image = UserImage.objects.get(user=user)
        assert retrieved_image.user == user

        # Delete (cascade from user)
        user.delete()
        assert not UserImage.objects.filter(id=user_image.id).exists()


@pytest.mark.django_db
class TestModelRelationships:
    """Test model relationships."""

    def test_user_profile_relationship(self):
        """Test User-UserProfile relationship."""
        user = User.objects.create_user(email="<EMAIL>", password="testpass123")
        profile = UserProfile.objects.create(user=user, first_name="Relationship", last_name="Test")

        # Test forward relationship
        assert profile.user == user

        # Test reverse relationship (if exists)
        try:
            assert user.userprofile == profile
        except AttributeError:
            # Reverse relationship might not be defined
            pass

    def test_user_image_relationship(self):
        """Test User-UserImage relationship."""
        user = User.objects.create_user(email="<EMAIL>", password="testpass123")
        image_file = SimpleUploadedFile("relationship_test.jpg", b"fake image content", content_type="image/jpeg")
        user_image = UserImage.objects.create(user=user, image=image_file)

        # Test forward relationship
        assert user_image.user == user

        # Test that user can have multiple images
        image_file2 = SimpleUploadedFile("relationship_test2.jpg", b"fake image content 2", content_type="image/jpeg")
        user_image2 = UserImage.objects.create(user=user, image=image_file2)

        user_images = UserImage.objects.filter(user=user)
        assert user_images.count() == 2
